'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/IScroll/IScroll'], function(module, kernel, jquery, IScroll) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.protocol");	
	
	var scrollStar=true;
	var curPage=1;
	var totalPage;
	var addmenu= true;
	var myscroll;
	var ajaxplatformId='';
   $dom.scroll(function(){
   		if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
		var bheight = $(document).height();//获取窗口高度
		var sheight = kernel.getScrollHeight(dom);//获取滚动条高度，[0]是为了把jq对象转化为js对象
		var stop = $dom.scrollTop();//滚动条距离顶部的距离
		if(stop>=sheight-bheight && stop>1 && scrollStar){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时;
			scrollStar=false;
			if(curPage >= totalPage){
				$(".load-tip").css("display","none");
				$(".protocol-list").append("<p class='nomoredatatip' style='text-align:center;padding:10px 0 25px 0;'>无更多数据</p>")
			}else{
				curPage++;
				getdatalist(curPage);
			}
			
		}
	});


	function getdatalist(){
		$('.load-tip').css('display','block')
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/agreement/getAgreementSkuList?sort=1",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,offset:curPage,limit:10,platformId:ajaxplatformId},
			dataType: "json",
			success: function(res) {
				if(curPage == 1 && res.data.rows.length <= 0 && ajaxplatformId==''){
					$(".nodata").show();
					$('.load-tip').css('display','none');
					$('.menu-box').hide();
					$(".tosignpro").click(function(){
						window.location.href="ybmaction://finish?type=0,ybmpage://commonh5activity?umkey=1111hd2&url="+apiUrl+"/static/?ybm_title=协议列表&cache=0&head_menu=0#!protocollist&issign=0"
					})
					return;
				}else if(curPage == 1 && res.data.rows.length > 0){
					$(".protocol-banner").show();
					$(".protocol-menu").show();
					$dom.animate({
						scrollTop:0
					},0);
					$(".protocol-cont").empty();
					$('.nomoredatatip').hide()
				}
				if(addmenu){
					var menuTemp = '';
					var menuTarget=$(".protocol-menu .fixed-box");
					if(res.agreementList.length == 1){
						menuTemp +='<div class="menu-box"><div class="menu-scroll"><div class="menu-list less-list"><div class="menu-item activeted">'+res.agreementList[0].name+'</div></div></div></div>'
						menuTarget.append(menuTemp)
					}else{
						menuTemp +='<div class="menu-box"><div class="menu-scroll"><div class="menu-list">'
						menuTemp +='<div class="menu-item activeted" protocol-id=" ">全部商品协议</div>'		
							for(var i=0;i<res.agreementList.length;i++){
								menuTemp+='<div class="menu-item" protocol-id="'+res.agreementList[i].id+'">'+res.agreementList[i].name+'</div>'
							}
						menuTemp+='</div></div><div class="more-list-box"><div class="more-list">'
						menuTemp+='<div class="more-menu-item cur" protocol-id=" ">全部商品协议</div>'				
							for(var i=0;i<res.agreementList.length;i++){
								menuTemp+='<div class="more-menu-item" protocol-id="'+res.agreementList[i].id+'">'+res.agreementList[i].name+'</div>'
							}
						menuTemp+='</div></div><div class="token"><div class="tokens"></div><div class="tokens"></div><div class="tokens"></div></div></div>'
						menuTarget.append(menuTemp);
					}
					$(".protocol-menu .token").unbind();
					$(".protocol-menu .token").bind('click',function(){
						var morelistheight = window.innerHeight-40;
						$(".protocol-menu .more-list").css("height",morelistheight+'px')
						$(".protocol-menu .more-list-box").toggle();
					});
					$(".protocol-menu .menu-list").unbind()
					$(".protocol-menu .menu-list").bind('click',function(e){
						var index = $(e.target).index();
						var offsetLeft = -parseInt(e.target.offsetLeft);
						offsetLeft = index >0 ? offsetLeft+20 : offsetLeft;
						myscroll.scrollTo(offsetLeft, 0, 400);
						$(e.target).addClass('activeted').siblings().removeClass("activeted");
						$($('.protocol-menu .more-list .more-menu-item')[index]).addClass('cur').siblings().removeClass("cur");
						ajaxplatformId = $(e.target).attr('protocol-id');
						curPage =1;
						getdatalist();
					})
					$(".protocol-menu .more-list").unbind()
					$(".protocol-menu .more-list").bind('click',function(e){
						var index = $(e.target).index();
						var offsetLeft = -parseInt($('.protocol-menu .menu-list .menu-item')[index].offsetLeft);
						$($('.protocol-menu .more-list .more-menu-item')[index]).addClass('cur').siblings().removeClass("cur");
						$($('.protocol-menu .menu-list .menu-item')[index]).addClass('activeted').siblings().removeClass("activeted");
						offsetLeft = index >0 ? offsetLeft+20 : offsetLeft;
						myscroll.scrollTo(offsetLeft, 0, 400);
						$(".protocol-menu .more-list-box").hide();
						ajaxplatformId = $(e.target).attr('protocol-id');
						curPage =1;
						getdatalist();
					})
					$('.protocol-menu .more-list-box').click(function(){
						$(".protocol-menu .more-list-box").hide()
					})
					myscroll=new IScroll(".protocol-menu .menu-scroll",{scrollX: true, scrollbars: false, click: true });
					addmenu = false;
				}
				var protocolTemp = " ";
				var protocolTarget = $(".protocol-cont");
				totalPage = res.data.pageCount;
				$('.load-tip').css('display','none')
				
				if(res.data.rows){
					for(var i=0;i<res.data.rows.length;i++){
						protocolTemp+='<div class="protocol-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
						if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
							protocolTemp += '<div class="control-box">';
								if(res.data.rows[i].uniformPrice){
									protocolTemp += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
								};
								if(res.data.rows[i].suggestPrice){
									protocolTemp += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
								};
								if(res.data.rows[i].grossMargin){
									protocolTemp += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
								};
							protocolTemp+='</div></a>'
						}
						protocolTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
							if(res.data.rows[i].agent == 1){
								protocolTemp+='<div class="dujia">独家</div>'
							}
						protocolTemp+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
						protocolTemp+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
						protocolTemp+='<div class="biaoqian">';
						if(res.data.rows[i].tagList){
							for(var j in res.data.rows[i].tagList.slice(0,3)){
								switch(res.data.rows[i].tagList[j].uiType){
									case 1:protocolTemp+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
									case 2:protocolTemp+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
									case 3:protocolTemp+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
									case 4:protocolTemp+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
									default:;break;
								}
							}
						}
						protocolTemp+='</div>'
							if(res.data.rows[i].isControl==1){
									if(res.data.rows[i].isPurchase==true){
										if(res.data.rows[i].isControlPriceToMe==1){
											protocolTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											if(res.data.rows[i].priceType==1){
												protocolTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
											}else{
												protocolTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
											}
										}
										
											// if (res.data.rows[i].isSplit==0) {
												// protocolTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
											/*}else{
												protocolTemp+='<div class="zhonbao-des"></div>';
											}*/
												protocolTemp+='<div class="handle">\
														<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
														<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
														<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
													</div>';
											
									}else{
										protocolTemp+='<div class="price">暂无购买权限</div>';
										protocolTemp += '<div class="control-box"></div>';
										// protocolTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
										protocolTemp+='<div class="handle-lon"></div>';
									}
							}else{
								if(res.data.rows[i].isControlPriceToMe==1){
									protocolTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										protocolTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										protocolTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
					
								// if (res.data.rows[i].isSplit==0) {
									// protocolTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
								/*}else{
									protocolTemp+='<div class="zhonbao-des"></div>';
								}*/
									protocolTemp+='<div class="handle">\
											<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
											<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
											<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
										</div>';
								
							}
							
						
							if(res.data.rows[i].status=="2"){
								protocolTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
							};
							if(res.data.rows[i].markerUrl){
								protocolTemp+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
							}
							
						protocolTemp+='</div>';
					}
				}


				protocolTarget.append(protocolTemp);
				
				rowAddMin();

				kernel.hideLoading();
				scrollStar = true;
			}
		});
	}


	getdatalist()
	
	return {
		onload: function(force) {
			//before open this page
			setAppTitle('协议专区');
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});