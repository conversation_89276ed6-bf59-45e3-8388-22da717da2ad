'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/IScroll/IScroll'], function(module, kernel, jquery, IScroll) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.goldpro");	
	var myscroll=new IScroll(".tab-title",{scrollX: true, scrollbars: false, click: true });
	var scrollTop1 = $('.goldpro .tab-section').offset().top;
	var itemIndex = 0 ;
	var typeCodeArr=['02t_xn','02t_wg','02t_fs','02t_kj','02t_ek','02t_hx','ZS201803071009563863','ZS201803271558542151','ZS201803021125262307','02t_zb','02t_kg','ZS201805021641412730'];
	var chooseCode = typeCodeArr[0];
	// 打开页面，跳转到制定的tab栏
	var tabIndex = kernel.location.args.tabindex;
	if(tabIndex){
		itemIndex = tabIndex - 1;
		chooseCode = typeCodeArr[itemIndex];
		changeClass();
		tabscroll();
	}
	$(".tab-title-arrow").click(function(){
		$(".tab-title").toggle();
		$(".tab-title-section").toggle();
		$(".tab-more-section").toggle();
		$(this).toggleClass("open");
		
	});
	// $('.goldpro .tab-section').click(function(){
		
	// 	$dom.animate({  
 //            scrollTop: scrollTop1
 //        }, 0);
 //        $('.goldpro .tab-section').css("position","fixed");
	// })

   $dom.scroll(function(){
   		if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
		
		var sHtop = $(dom).scrollTop();
		if(sHtop >= scrollTop1){
			$('.goldpro .tab-section').css("position","fixed");
			$(".baktitlebox").show();

		}else{
			$('.goldpro .tab-section').css("position","relative");
			$('.goldpro .tab-title').css("display",'block');
			$(".baktitlebox").hide();
		}
		$('.goldpro .tab-more-section').css('display',"none");
		$('.goldpro .tab-title').css('display',"block");
		$('.goldpro .tab-title-section').css('display',"none");
		$('.goldpro .tab-title-arrow').removeClass("open")
	});


	function getdatalist(){
		$('.load-tip').css('display','block')
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId="+chooseCode+"&sort=1&limit=30",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,offset:0},
			dataType: "json",
			success: function(res) {
				var goldproTemp2 = " ";
				var goldproTarget2 = $(".goldpro-cont2");
				if(res.isDig==1){
					$(".wajinbibtn").show();
					$(".wajinbibtn").attr("data-id",res.digId);
					getjinbi(thispage);
				}else{
					$(".wajinbibtn").hide();
				}

				$('.load-tip').css('display','none')
				
				if(res.data.rows){
					for(var i=0;i<res.data.rows.length;i++){
						goldproTemp2+='<div class="goldpro-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
						if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
							goldproTemp2 += '<div class="control-box">';
								if(res.data.rows[i].uniformPrice){
									goldproTemp2 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
								};
								if(res.data.rows[i].suggestPrice){
									goldproTemp2 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
								};
								if(res.data.rows[i].grossMargin){
									goldproTemp2 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
								};
							goldproTemp2+='</div></a>'
						}
						goldproTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
							if(res.data.rows[i].agent == 1){
								goldproTemp2+='<div class="dujia">独家</div>'
							}
						goldproTemp2+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
						goldproTemp2+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
						goldproTemp2+='<div class="biaoqian">';
						if(res.data.rows[i].tagList){
							for(var j in res.data.rows[i].tagList.slice(0,3)){
								switch(res.data.rows[i].tagList[j].uiType){
									case 1:goldproTemp2+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
									case 2:goldproTemp2+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
									case 3:goldproTemp2+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
									case 4:goldproTemp2+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
									default:;break;
								}
							}
						}
						goldproTemp2+='</div>'
							if(res.data.rows[i].isControl==1){
									if(res.data.rows[i].isPurchase==true){
										if(res.data.rows[i].isControlPriceToMe==1){
											goldproTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											if(res.data.rows[i].priceType==1){
												goldproTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
											}else{
												goldproTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
											}
										}
										
											// if (res.data.rows[i].isSplit==0) {
												// goldproTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
											/*}else{
												goldproTemp2+='<div class="zhonbao-des"></div>';
											}*/
												goldproTemp2+='<div class="handle">\
														<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
														<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
														<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
													</div>';
											
									}else{
										goldproTemp2+='<div class="price">暂无购买权限</div>';
										goldproTemp2 += '<div class="control-box"></div>';
										// goldproTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
										goldproTemp2+='<div class="handle-lon"></div>';
									}
							}else{
								if(res.data.rows[i].isControlPriceToMe==1){
									goldproTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										goldproTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										goldproTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
					
								// if (res.data.rows[i].isSplit==0) {
									// goldproTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
								/*}else{
									goldproTemp2+='<div class="zhonbao-des"></div>';
								}*/
									goldproTemp2+='<div class="handle">\
											<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
											<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
											<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
										</div>';
								
							}
							
						
							if(res.data.rows[i].status=="2"){
								goldproTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
							};
							if(res.data.rows[i].markerUrl){
								goldproTemp2+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
							}
							
						goldproTemp2+='</div>';
					}
				}


				goldproTarget2.append(goldproTemp2);
				
				rowAddMin();

				kernel.hideLoading();
				
				changebtnHtml();

				$('.goldpro .goldpro-btn').unbind();
				$('.goldpro .goldpro-btn').click(function(){
					$(".goldpro-cont2").empty();
					kernel.showLoading();
					getdatalist();
					tabscroll();
					changeClass();
					$dom.animate({  
			            scrollTop: scrollTop1
			        }, 0);
			        $('.goldpro .tab-section').css("position","fixed");
			        $(".baktitlebox").show();
				})
			}
		});
	}
	
	function tabscroll(){
		switch(itemIndex){
			case 0:myscroll.scrollTo(0, 0, 400);break;
			case 1:myscroll.scrollTo(0, 0, 400);break;
			case 2:myscroll.scrollTo(0, 0, 400);break;
			case 3:myscroll.scrollTo(-120, 0, 400);break;
			case 4:myscroll.scrollTo(-200, 0, 400);break;
			case 5:myscroll.scrollTo(-285, 0, 400);break;
			case 6:myscroll.scrollTo(-350, 0, 400);break;
			case 7:myscroll.scrollTo(-390, 0, 400);break;
			case 8:myscroll.scrollTo(-440, 0, 400);break;
			case 9:myscroll.scrollTo(-490, 0, 400);break;
			case 10:myscroll.scrollTo(-560, 0, 400);break;
			case 11:myscroll.scrollTo(-560, 0, 400);break;
			default:;break;
		}
	}

	function changebtnHtml(){
		switch(itemIndex){
			case 0:$('.goldpro-btn').html('点击跳转至“五官科”');chooseCode="02t_wg";itemIndex = 1;break;
			case 1:$('.goldpro-btn').html('点击跳转至“解热镇痛”');chooseCode="02t_fs";itemIndex = 2;break;
			case 2:$('.goldpro-btn').html('点击跳转至“抗菌消炎”');chooseCode="02t_kj";itemIndex = 3;break;
			case 3:$('.goldpro-btn').html('点击跳转至“儿科热卖”');chooseCode="02t_ek";itemIndex = 4;break;
			case 4:$('.goldpro-btn').html('点击跳转至“呼吸系统”');chooseCode="02t_hx";itemIndex = 5;break;
			case 5:$('.goldpro-btn').html('点击跳转至“抗病毒”');chooseCode="ZS201803071009563863";itemIndex = 6;break;
			case 6:$('.goldpro-btn').html('点击跳转至“抗糖尿病”');chooseCode="ZS201803271558542151";itemIndex = 7;break;
			case 7:$('.goldpro-btn').html('点击跳转至“泌尿系统”');chooseCode="ZS201803021125262307";itemIndex = 8;break;
			case 8:$('.goldpro-btn').html('点击跳转至“维矿物质”');chooseCode="02t_zb";itemIndex = 9;break;
			case 9:$('.goldpro-btn').html('点击跳转至“抗感冒”');chooseCode="02t_kg";itemIndex = 10;break;
			case 10:$('.goldpro-btn').html('点击跳转至“医疗器械”');chooseCode="ZS201805021641412730";itemIndex = 11;break;
			case 11:$('.goldpro-btn').html('点击跳转至“心脑血管”');chooseCode="02t_xn";itemIndex = 0;break;
			default:break;
		}
	}

	function changeClass(){
		$('.tab-title-item').eq(itemIndex).addClass('cur').siblings().removeClass('cur');
		$('.tab-more-item').eq(itemIndex).addClass('cur').siblings().removeClass('cur');
	}
	$(".tab-more-item").click(function(){
		$(".tab-title").show();
		$(".tab-title-section").hide();
		$(".tab-more-section").hide();
		$(".tab-title-arrow").removeClass("open");
		itemIndex = $(this).index();
		chooseCode = typeCodeArr[itemIndex];
		$(".goldpro-cont2").empty();
		getdatalist();
		kernel.showLoading();
		tabscroll();
		changeClass();
	});

	$('.tab-title-item').click(function(){
		itemIndex = $(this).index();
		chooseCode = typeCodeArr[itemIndex];
		$(".goldpro-cont2").empty();
		getdatalist();
		kernel.showLoading();
		tabscroll();
		changeClass();

		$dom.animate({  
            scrollTop: scrollTop1
        }, 0);
        $('.goldpro .tab-section').css("position","fixed");
        $(".baktitlebox").show();
	})

	getdatalist()
	
	return {
		onload: function(force) {
			setAppTitle("黄金单品");
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});