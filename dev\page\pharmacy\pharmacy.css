#page > .content > .pharmacy {
  border-top: none;
  background: #f8f8f8;
  overflow-y: auto;
}
#page > .content > .pharmacy .fixed-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  overflow: visible;
  z-index: 20;
}
#page > .content > .pharmacy .fixed-box .tab-section {
  width: 100%;
  background: #fff;
  overflow: visible;
  height: 40px;
  z-index: 210;
}
#page > .content > .pharmacy .fixed-box .tab-section .tab-title {
  position: relative;
  width: 100%;
  height: 40px;
  background: linear-gradient(90deg, #485563, #29323C);
  display: flex;
  display: -webkit-flex;
  justify-content: center;
  -webkit-justify-content: center;
}
#page > .content > .pharmacy .fixed-box .tab-section .tab-title ul {
  padding: 0;
  margin: 0;
  height: 40px;
  width: 100%;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
}
#page > .content > .pharmacy .fixed-box .tab-section .tab-title .tab-title-item {
  display: inline-block;
  height: 40px;
  width: 25%;
  float: left;
  line-height: 40px;
  font-size: 14px;
  color: #fff;
  position: relative;
  text-align: center;
}
#page > .content > .pharmacy .fixed-box .tab-section .tab-title .tab-title-item.cur {
  color: #00c775;
  font-size: 16px;
  font-weight: 700;
}
#page > .content > .pharmacy .fixed-box .tab-section .tab-title .tab-title-item.cur:after {
  content: '';
  display: inline-block;
  position: absolute;
  width: 80%;
  left: 50%;
  margin-left: -40%;
  bottom: 0;
  border-bottom: 2px solid #00c775;
  z-index: 2;
}
#page > .content > .pharmacy .fixed-box .total-video {
  height: 40px;
  overflow: visible;
  position: relative;
  background: #fff;
  z-index: 20;
}
#page > .content > .pharmacy .fixed-box .total-video .title {
  font-size: 14px;
  color: #666;
  line-height: 40px;
  float: right;
  padding-right: 10px;
  width: 60%;
}
#page > .content > .pharmacy .fixed-box .total-video .filter {
  font-size: 0;
  color: #666;
  float: left;
  padding: 0 10px 0 14px;
  border-right: 1px solid #eee;
}
#page > .content > .pharmacy .fixed-box .total-video .filter i {
  font-size: 14px;
  float: left;
  line-height: 40px;
}
#page > .content > .pharmacy .fixed-box .total-video .filter span {
  width: 30px;
  height: 40px;
  float: right;
  background: url('images/icon-up.png') center center no-repeat;
  background-size: 50%;
  transition: 0.15s;
}
#page > .content > .pharmacy .fixed-box .total-video .filter span.rotatespan {
  transform: rotate(180deg);
  transform: -webkit-rotate(180deg);
}
#page > .content > .pharmacy .fixed-box .total-video .menubox {
  position: absolute;
  left: 0;
  top: 40px;
  width: 100%;
  z-index: 100;
  display: none;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levelonemenu {
  background: #F0F2F5;
  font-size: 0;
  width: 35%;
  text-align: center;
  float: left;
  max-height: 400px;
  overflow-y: auto;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levelonemenu li {
  border-bottom: 1px solid #fff;
  display: inline-block;
  line-height: 40px;
  font-size: 14px;
  width: 100%;
  color: #333;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levelonemenu li.activeted {
  color: #00DC82;
  font-weight: 700;
  background: #fff;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levelonemenu li:last-child {
  border-bottom: none;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levletwomenu {
  background: #fff;
  float: right;
  width: 65%;
  font-size: 0;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levletwomenu li {
  border-bottom: 1px solid #eee;
  display: inline-block;
  line-height: 40px;
  width: 100%;
  font-size: 14px;
  color: #333;
  padding: 0 20px 0 16px;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levletwomenu li.cur {
  color: #00DC82;
  font-weight: 700;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levletwomenu li:last-child {
  border-bottom: none;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levletwomenu li span {
  display: inline-block;
  width: 15px;
  height: 10px;
  border-left: 2px solid #00DC82;
  border-bottom: 2px solid #00DC82;
  transform: translateY(13px) rotate(-45deg);
  -webkit-transform: translateY(13px) rotate(-45deg);
  float: right;
  display: none;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .levletwomenu li:first-child span {
  display: block;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .guideUl {
  background: #fff;
  font-size: 0;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .guideUl li {
  border-bottom: 1px solid #eee;
  display: inline-block;
  line-height: 40px;
  font-size: 14px;
  width: 100%;
  padding: 0 10px;
  color: #333;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .guideUl li.cur {
  color: #00DC82;
  font-weight: 700;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .guideUl li:last-child {
  border-bottom: none;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .guideUl li span {
  display: inline-block;
  width: 15px;
  height: 10px;
  border-left: 2px solid #00DC82;
  border-bottom: 2px solid #00DC82;
  transform: translateY(13px) rotate(-45deg);
  -webkit-transform: translateY(13px) rotate(-45deg);
  float: right;
  display: none;
}
#page > .content > .pharmacy .fixed-box .total-video .menubox .guideUl li:first-child span {
  display: block;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item {
  padding: 9px 10px 8px 15px;
  background: #fff;
  margin-top: 10px;
  display: flex;
  display: -webkit-flex;
  position: relative;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .video-mesg {
  width: 55%;
  height: 90px;
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  -webkit-flex-direction: column;
  justify-content: space-between;
  -webkit-justify-content: space-between;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .video-mesg .title {
  font-size: 16px;
  line-height: 22px;
  color: #333;
  font-weight: 700;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  white-space: normal;
  height: 46px;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .video-mesg .time-watch {
  font-size: 12px;
  line-height: 16px;
  color: #999;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .video-mesg .time-watch .time {
  float: left;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .video-mesg .time-watch .watchtimes {
  float: right;
  padding: 0 10px 0 20px;
  background: url('images/eye.png') left 2px no-repeat;
  background-size: 25% 70%;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .img-box {
  flex: 1;
  -webkit-flex: 1;
  margin-left: 20px;
  position: relative;
  height: 90px;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .img-box img {
  border-radius: 4px;
  height: 90px;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .img-box a {
  display: inline-block;
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  background: url('images/play.png') center center no-repeat;
  background-size: 18%;
}
#page > .content > .pharmacy .pharmacy-list .pharmacy-item .linka {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  height: 100px;
  z-index: 2;
}
