'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/ProgressBar/ProgressBar'], function(module, kernel, jquery, ProgressBar) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);

	var setImer;

	// 格式化时间
	function timeStamp2( second_time ){  
  		var dtime;
		second_time = parseInt(second_time);
		var hour = parseInt( parseInt(second_time / 60) /60 );  
	    if( hour > 24 ){  
	        var day = parseInt( parseInt( parseInt(second_time / 60) /60 ) / 24 );  
	        dtime=day + "天";
	    }else{
	    	dtime="0天";
	    }
		return dtime;          
	} 


		function timeStamp( second_time ){  
  		var time,dtime;
		second_time = parseInt(second_time);
		if (second_time<10){
			second_time = "0"+second_time;
		}
		time = "<span class='abo'>00</span>:<span class='abo'>00</span>:<span class='abo'>"+second_time+"</span>";
		if( parseInt(second_time )> 60){  
		  
		    var second = parseInt(second_time) % 60;  
		    var min = parseInt(second_time / 60);  
		    if (second<10){
				second = "0"+second;
			}
			if (min<10){
				min = "0"+min;
			}
		    time = "<span class='abo'>00</span>:<span class='abo'>"+ min + "</span>:<span class='abo'>" + second +"</span>";  
		      
		    if( min > 60 ){  
		        min = parseInt(second_time / 60) % 60;  
		        var hour = parseInt( parseInt(second_time / 60) /60 );  
		        if (hour<10){
					hour = "0"+hour;
				}
				if (min<10){
					min = "0"+min;
				}
		        time = "<span class='abo'>"+hour + "</span>:<span class='abo'>" + min + "</span>:<span class='abo'>" + second+"</span>";  
		  		
		  		


		        if( hour > 24 ){  
		            hour = parseInt( parseInt(second_time / 60) /60 ) % 24;  
		            
					if (hour<10){
						hour = "0"+hour;
					}
		            time = " <span class='abo'>" + hour + "</span>:<span class='abo'>" + min + "</span>:<span class='abo'>" + second+"</span>";  
		        }  
		    }  
		      
		  
		}  
		//return dtime;
		return time;          
		} 

		function setcountdown(timer,dom,time){
			clearInterval(timer)
			timer = setInterval(function(){
				time--;
				dom.html(timeStamp(time));
			},1000);
		}
		
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/voucher/findVoucherActivity",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId},
			dataType: "json",
			success: function(res) {
				var baotemp=" ";
				var baotarget=$(".hb-box2");
				if(res.status=="success"){
					for(var i=0; i<res.voucherList.length; i++){
						//已经结束
						if(res.voucherList[i].state==3 || res.voucherList[i].state==4 || res.voucherList[i].endTimeStamp==0 || res.voucherList[i].provideTotalCount/res.voucherList[i].totalLimitQty==1){
							baotemp='<div class="redbao-item redbao-item4">\
							<div class="redbao-title">'+res.voucherList[i].remark+'</div>\
							<div class="redbao-cont">\
								<div class="redbao-cont-l">\
									<div class="redbao-amount">\
										<span class="redbao-amount-unt">¥</span>\
										<span class="redbao-amount-num">'+res.voucherList[i].moneyInVoucher+'</span>\
									</div>\
									<div class="redbao-plain">\
										<div class="redbao-plain-top">满'+res.voucherList[i].minMoneyToEnable+'元使用</div>\
										<div class="redbao-plain-cen"><img src="/static/public/events/0730/hb4.jpg"></div>\
										<div class="redbao-plain-bot">'+res.voucherList[i].simpleDesc+'</div>\
									</div>\
								</div>\
								<div class="redbao-cont-r">\
									<div class="redbao-type redbao-type4"></div>\
								</div>\
							</div>\
						</div>';
						baotarget.append(baotemp);
						}else{
							//未开始 倒计时
							if(res.voucherList[i].startTimeStamp > 0){
								baotemp='<div class="redbao-item redbao-item1">\
								<div class="redbao-title">'+res.voucherList[i].remark+'</div>\
								<div class="redbao-cont">\
									<div class="redbao-cont-l">\
										<div class="redbao-amount">\
											<span class="redbao-amount-unt">¥</span>\
											<span class="redbao-amount-num">'+res.voucherList[i].moneyInVoucher+'</span>\
										</div>\
										<div class="redbao-plain">\
											<div class="redbao-plain-top">满'+res.voucherList[i].minMoneyToEnable+'元使用</div>\
											<div class="redbao-plain-cen"><img src="/static/public/events/0730/hb4.jpg"></div>\
											<div class="redbao-plain-bot">'+res.voucherList[i].simpleDesc+'</div>\
										</div>\
									</div>\
									<div class="redbao-cont-r">\
										<div class="redbao-type redbao-type1">\
											<div class="redbao-type-text">\
												<div>距离开抢</div>\
												<div>还剩<span class="cutday cutday'+res.voucherList[i].id+'"></span></div>\
												<div class="cutime cutime'+res.voucherList[i].id+'"></div>\
											</div>\
										</div>\
									</div>\
								</div>\
							</div>';
							baotarget.append(baotemp);
								var timesVal=null;
								var cutimeId=null;
								cutimeId = $(".cutime"+res.voucherList[i].id);
								timesVal = res.voucherList[i].startTimeStamp/1000;
								var setImer = 'setImer'+res.voucherList[i].id;
								/*setImer = setInterval(function(){
									timesVal--;
									cutimeId.html(timeStamp(timesVal));
								},1000);*/
								setcountdown(setImer,cutimeId,timesVal)

								$(".cutday"+res.voucherList[i].id).text(timeStamp2(timesVal));

							}

							
							//已经领取
							if(res.voucherList[i].isLq==1){
								baotemp='<div class="redbao-item redbao-item3">\
								<div class="redbao-title">'+res.voucherList[i].remark+'</div>\
								<div class="redbao-cont">\
									<div class="redbao-cont-l">\
										<div class="redbao-amount">\
											<span class="redbao-amount-unt">¥</span>\
											<span class="redbao-amount-num">'+res.voucherList[i].moneyInVoucher+'</span>\
										</div>\
										<div class="redbao-plain">\
											<div class="redbao-plain-top">满'+res.voucherList[i].minMoneyToEnable+'元使用</div>\
											<div class="redbao-plain-cen"><img src="/static/public/events/0730/hb4.jpg"></div>\
											<div class="redbao-plain-bot">'+res.voucherList[i].simpleDesc+'</div>\
										</div>\
									</div>\
									<div class="redbao-cont-r">\
										<div class="redbao-type redbao-type3"></div>\
									</div>\
								</div>\
							</div>';
							baotarget.append(baotemp);
							}else{
								//已经开始 进度条
								if(res.voucherList[i].startTimeStamp == 0 && res.voucherList[i].endTimeStamp >0){
									baotemp='<div class="redbao-item redbao-item2" data-id="'+res.voucherList[i].id+'">\
									<div class="redbao-title">'+res.voucherList[i].remark+'</div>\
									<div class="redbao-cont">\
										<div class="redbao-cont-l">\
											<div class="redbao-amount">\
												<span class="redbao-amount-unt">¥</span>\
												<span class="redbao-amount-num">'+res.voucherList[i].moneyInVoucher+'</span>\
											</div>\
											<div class="redbao-plain">\
												<div class="redbao-plain-top">满'+res.voucherList[i].minMoneyToEnable+'元使用</div>\
												<div class="redbao-plain-cen"><img src="/static/public/events/0730/hb4.jpg"></div>\
												<div class="redbao-plain-bot">'+res.voucherList[i].simpleDesc+'</div>\
											</div>\
										</div>\
										<div class="redbao-cont-r">\
											<div class="redbao-type redbao-type2">\
												<div class="cro-pross cro-pross'+res.voucherList[i].id+'">\
													<div class="cro-pross-text">\
														已抢<br ><span class="cro-pross-num">'+((res.voucherList[i].provideTotalCount/res.voucherList[i].totalLimitQty).toFixed(3)).substring(0, 4)*100+'</span>%\
													</div>\
												</div>\
											</div>\
										</div>\
									</div>\
								</div>';
								baotarget.append(baotemp);
								// $(".tip-loading").hide();
									var bar = new ProgressBar.SemiCircle(".cro-pross"+res.voucherList[i].id, {
									  strokeWidth: 6,
									  easing: 'easeInOut',
									  duration: 1400,
									  color: '#fdff61',
									  trailColor: '#fec298',
									  trailWidth: 6,
									  svgStyle: null
									});
									bar.animate((res.voucherList[i].provideTotalCount/res.voucherList[i].totalLimitQty).toFixed(1)); 

									
								}
							};
						}

						
					}

					$(".redbao-item2").click(function(){
						var thisemnt = $(this);
						var baoId = thisemnt.attr("data-id");
						$.ajax({
							type: "POST",
							url: apiUrl+"/app/voucher/receiveVoucher",
							headers : {'version':version,'terminalType':terminalType},
							data: {"merchantId":merchantId,voucherTemplateId:baoId},
							dataType: "json",
							success: function(data) {
								if (data.status=="success") {
									thisemnt.find(".redbao-type2").empty().removeClass("redbao-type2").addClass("redbao-type3");
									thisemnt.removeClass("redbao-item2").addClass("redbao-item3");
								}else{
									kernel.hint(data.msg);
								}
							}
						});
					});	
					$(".redbao-item3").click(function(){
						kernel.hint("用户已领取此券");
					})
				}
			}
		})

	return {
		onload: function(force) {
			
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});