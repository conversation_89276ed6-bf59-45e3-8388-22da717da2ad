'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.hzsichongzou");

$(".cqxrqbtn").click(function(){
	var baoId = $(this).attr("data-id");
	
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/voucher/receiveVoucher",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId,voucherTemplateId:baoId},
		dataType: "json",
		success: function(data) {
			if (data.status=="success") {
				$(".redpopup1").show();
				//$("#page>.content>.hzsichongzou").css('overflow-y','inherit');
			}else{
				if(data.msg){
					$(".redpopup2 .text").text(data.msg);
					$(".redpopup2").show();
					//$("#page>.content>.hzsichongzou").css('overflow-y','inherit');
				}else{
					$(".redpopup2 .text").text(data.errorMsg);
					$(".redpopup2").show();
					//$("#page>.content>.hzsichongzou").css('overflow-y','inherit');
				}
				
			}
		}
	});
});

$(".redpopup").click(function(){
	$(this).hide();
	$("#page>.content>.hzsichongzou").css('overflow-y','auto');
});
	
	
	return {
		onload: function(force) {
			
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
           
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});