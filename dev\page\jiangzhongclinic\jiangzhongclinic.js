'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/IScroll/IScroll'], function(module, kernel, jquery, IScroll) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.jiangzhongclinic");	


   $dom.scroll(function(){
   		if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
	});


	function getdatalist(code,dom){
		$('.load-tip').css('display','block')
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId="+code+"&sort=1&limit=10",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,offset:0},
			dataType: "json",
			success: function(res) {
				var jiangzhongclinicTemp = " ";
				var jiangzhongclinicTarget = $(dom);
				$('.load-tip').css('display','none');
				for(var i=0;i<res.data.rows.length;i++){
					jiangzhongclinicTemp+='<div class="jiangzhongclinic-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
					if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
						jiangzhongclinicTemp += '<div class="control-box">';
							if(res.data.rows[i].uniformPrice){
								jiangzhongclinicTemp += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
							};
							if(res.data.rows[i].suggestPrice){
								jiangzhongclinicTemp += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
							};
							if(res.data.rows[i].grossMargin){
								jiangzhongclinicTemp += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
							};
						jiangzhongclinicTemp+='</div></a>'
					}
					jiangzhongclinicTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							jiangzhongclinicTemp+='<div class="dujia">独家</div>'
						}
					jiangzhongclinicTemp+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
					jiangzhongclinicTemp+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
					jiangzhongclinicTemp+='<div class="biaoqian">';
					if(res.data.rows[i].tagList){
						for(var j in res.data.rows[i].tagList.slice(0,3)){
							switch(res.data.rows[i].tagList[j].uiType){
								case 1:jiangzhongclinicTemp+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
								case 2:jiangzhongclinicTemp+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
								case 3:jiangzhongclinicTemp+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
								case 4:jiangzhongclinicTemp+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
								default:;break;
							}
						}
					}
					jiangzhongclinicTemp+='</div>'
						if(res.data.rows[i].isControl==1){
								if(res.data.rows[i].isPurchase==true){
									if(res.data.rows[i].isControlPriceToMe==1){
										jiangzhongclinicTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										if(res.data.rows[i].priceType==1){
											jiangzhongclinicTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											jiangzhongclinicTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
										}
									}
									
										// if (res.data.rows[i].isSplit==0) {
											// jiangzhongclinicTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
										/*}else{
											jiangzhongclinicTemp+='<div class="zhonbao-des"></div>';
										}*/
											jiangzhongclinicTemp+='<div class="handle">\
													<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
													<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
													<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
												</div>';
										
								}else{
									jiangzhongclinicTemp+='<div class="price">暂无购买权限</div>';
									jiangzhongclinicTemp += '<div class="control-box"></div>';
									// jiangzhongclinicTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
									jiangzhongclinicTemp+='<div class="handle-lon"></div>';
								}
						}else{
							if(res.data.rows[i].isControlPriceToMe==1){
								jiangzhongclinicTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								if(res.data.rows[i].priceType==1){
									jiangzhongclinicTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									jiangzhongclinicTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
								}
							}
				
							// if (res.data.rows[i].isSplit==0) {
								// jiangzhongclinicTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
							/*}else{
								jiangzhongclinicTemp+='<div class="zhonbao-des"></div>';
							}*/
								jiangzhongclinicTemp+='<div class="handle">\
										<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
										<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
										<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
									</div>';
							
						}
						
					
						if(res.data.rows[i].status=="2"){
							jiangzhongclinicTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
						};
						if(res.data.rows[i].markerUrl){
							jiangzhongclinicTemp+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
						}
						
					jiangzhongclinicTemp+='</div>';
				}


				jiangzhongclinicTarget.append(jiangzhongclinicTemp);
				
				rowAddMin();

				kernel.hideLoading();
			}
		});
	}

	getdatalist('ZS201803051500306390','.jiangzhongclinic-cont1');
	getdatalist('ZS201803051501549301','.jiangzhongclinic-cont2');
	
	return {
		onload: function(force) {
			setAppTitle("江中诊所专享特惠");
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});