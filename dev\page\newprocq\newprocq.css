#page > .content > .newprocq {
  overflow-y: auto;
  border-top: none;
  background: #4390e8;
  padding-bottom: 20px;
}
#page > .content > .newprocq .tab-title-box {
  position: relative;
}
#page > .content > .newprocq .tab-title-box .tab-title.fixed {
  position: fixed;
  top: 0;
  z-index: 999;
}
#page > .content > .newprocq .tab-title {
  background: #6a3879;
  color: #fff;
  width: 100%;
}
#page > .content > .newprocq .tab-title .tab-title-item {
  width: 50%;
  float: left;
  display: inline-block;
  text-align: center;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont {
  margin: 0 0.5%;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item {
  margin: 0.5%;
  width: 49%;
  background: #fff;
  border-radius: 4px;
  float: left;
  position: relative;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo {
  display: block;
  padding: 10px;
  position: relative;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo img {
  height: 140px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box {
  padding: 0 6px;
  position: absolute;
  left: 0;
  bottom: 0;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box span {
  font-size: 12px;
  padding: 2px 2px;
  line-height: 12px;
  color: #fff;
  border-radius: 4px 0 0 4px;
  background: #2b343f;
  display: inline-block;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box i {
  font-size: 12px;
  display: inline-block;
  background: #fff;
  color: #666;
  border: 1px solid #2b343f;
  padding: 0 2px;
  border-radius: 0 4px 4px 0;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box .control-price {
  float: left;
  display: flex;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box .control-price i {
  flex: 1;
  line-height: 14px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box .control-gross {
  float: left;
  display: flex;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box .control-gross i {
  flex: 1;
  line-height: 14px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box .control-gross span {
  margin-left: 2px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .commonName {
  height: 24px;
  display: block;
  line-height: 24px;
  padding-left: 5px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .commonName .dujia {
  float: left;
  color: #fff;
  padding: 1px 2px;
  font-size: 12px;
  line-height: 12px;
  border-radius: 3px;
  font-weight: 400;
  background: #27adff;
  margin-top: 5px;
  margin-right: 3px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .commonName .name {
  color: #333;
  font-size: 14px;
  line-height: 24px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .price {
  color: #ff2400;
  padding: 5px;
  font-weight: bold;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .norms {
  margin: 0 5px 3px;
  font-size: 12px;
  line-height: 14px;
  height: 14px;
  color: #999;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .norms .zhonbao-des {
  margin-left: 5px;
  display: inline;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .biaoqian {
  height: 20px;
  display: flex;
  flex-direction: rows;
  align-items: center;
  padding-left: 5px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .biaoqian div {
  padding: 1px 3px;
  margin-right: 5px;
  line-height: 12px;
  font-size: 12px;
  color: #fff;
  border-radius: 3px;
  display: inline-block;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .biaoqian div.quan {
  background: #ff0000;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .biaoqian div.linqi {
  background: #ff9400;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .biaoqian div.normal {
  color: #ff0e0e;
  padding: 0px 2px;
  border: 1px solid #ff9595;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .biaoqian div.fubiao {
  background: #e4e4e4;
  color: #333;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .handle {
  width: 94%;
  margin: 0px auto 8px;
  height: 27px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .handle .inp-total {
  display: inline-block;
  width: 35px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  opacity: 1;
  color: #333;
  border: none;
  font-size: 12px;
  background: #efefef;
  float: right;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .handle .min {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 15px 0 0 15px;
  color: #666;
  float: right;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .handle .add {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 0 15px 15px 0;
  color: #666;
  float: right;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .handle-lon {
  width: 94%;
  margin: 0 auto 8px;
  height: 27px;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .handle-lon .add-lon {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 50%;
  color: #666;
  float: right;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .gone-icon {
  display: block;
  position: absolute;
  width: 100%;
  height: 65%;
  background: url(images/<EMAIL>) center center no-repeat;
  background-size: 30%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
#page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .markerUrl {
  position: absolute;
  width: 40px;
  height: 48px;
  top: -2px;
  left: -1px;
}
@media (max-width: 480px) {
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo img {
    height: 166px;
  }
}
@media (max-width: 414px) {
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo img {
    height: 159px;
  }
}
@media (max-width: 412px) {
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo img {
    height: 158px;
  }
}
@media (max-width: 375px) {
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo img {
    height: 162px;
  }
  #page > .content > .newprocq .newprocq-list .newprocq-cont {
    min-height: 1419px;
  }
}
@media (max-width: 350px) {
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo img {
    height: 155px;
  }
  #page > .content > .newprocq .newprocq-list .newprocq-cont {
    min-height: 1383px;
  }
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box {
    height: 40px;
  }
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box span {
    width: 54px;
    text-align: center;
  }
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box i {
    text-align: center;
    padding: 0 8px;
  }
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box .control-gross span {
    margin-left: 0;
  }
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo .control-box .control-gross {
    margin-top: 3px;
  }
}
@media (max-width: 320px) {
  #page > .content > .newprocq .newprocq-list .newprocq-cont .newprocq-item .photo img {
    height: 135px;
  }
  #page > .content > .newprocq .newprocq-list .newprocq-cont {
    min-height: 1281px;
  }
}
