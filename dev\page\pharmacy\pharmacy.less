#page>.content>.pharmacy{
	border-top: none;
	background: #f8f8f8;
	overflow-y: auto;
	.fixed-box{
		position:fixed;
		top:0;
		left:0;
		width:100%;
		overflow:visible;
		z-index:20;
		.tab-section{
		    width: 100%;
		    background: #fff;
		    overflow: visible;
		    height:40px;
		    z-index: 210;
		    .tab-title{
				position: relative;
				width: 100%;
				height: 40px;
				background:linear-gradient(90deg,#485563,#29323C);
				display:flex;
				display:-webkit-flex;
				justify-content:center;
				-webkit-justify-content:center;
				ul{
					padding:0;
					margin:0;
				    height: 40px;
				    width:100%;
				    display:inline-block;
				    white-space: nowrap;
				    overflow: hidden;
				}
				.tab-title-item{
					display: inline-block;
					height: 40px;
					// padding:0 15px;
					width:25%;
					float: left;
					line-height: 40px;
					font-size: 14px;
					color: #fff;
					position: relative;
					text-align:center;
					&.cur{
						color: #00c775;
						font-size:16px;
						font-weight:700;
						&:after{
							content: '';
						    display: inline-block;
						    position: absolute;
						    width: 80%;
						    left: 50%;
						    margin-left: -40%;
						    bottom: 0;
						    border-bottom: 2px solid #00c775;
						    z-index: 2;
						}
						
					}
				}
			}
		}
		.total-video{
			height:40px;
			overflow:visible;
			position:relative;
			background:#fff;
			z-index:20;
			.title{
				font-size:14px;
				color:#666;
				line-height:40px;
				float:right;
				padding-right:10px;
				width:60%
			}
			.filter{
				font-size:0;
				color:#666;
				float:left;
				padding:0 10px 0 14px;
				border-right:1px solid #eee; 
				i{
					font-size:14px;
					float:left;
					line-height:40px;
				}
				span{
					width:30px;
					height:40px;
					float:right;
					background:url('./images/icon-up.png') center center no-repeat;
					background-size:50%;
					transition:0.15s;
					&.rotatespan{
						transform: rotate(180deg);
						transform: -webkit-rotate(180deg);
					}
				}
			}
			.menubox{
				position:absolute;
				left:0;
				top:40px;
				width:100%;
				z-index: 100;
				display:none;
				.levelonemenu{
					background: #F0F2F5;
					font-size:0;
					width:35%;
					text-align:center;
					float:left;
					max-height:400px;
					overflow-y:auto;
					li{
						border-bottom:1px solid #fff;
						display:inline-block;
						line-height:40px;
						font-size:14px;
						width:100%;
						color:#333;
						&.activeted{
							color: #00DC82;
							font-weight:700;
							background:#fff;
						}
						&:last-child{
							border-bottom:none;
						}
					}
				}
				.levletwomenu{
					background:#fff;
					float:right;
					width:65%;
					font-size:0;
					li{
						border-bottom:1px solid #eee;
						display:inline-block;
						line-height:40px;
						width:100%;
						font-size:14px;
						color:#333;
						padding:0 20px 0 16px;
						&.cur{
							color: #00DC82;
							font-weight:700;
						}
						&:last-child{
							border-bottom:none;
						}
						span{
							display:inline-block;
							width:15px;
							height:10px;
							border-left:2px solid #00DC82;
							border-bottom:2px solid #00DC82;
							transform:translateY(13px) rotate(-45deg);
							-webkit-transform:translateY(13px) rotate(-45deg);
							float:right;
							display:none;
						}
						&:first-child{
							span{
								display:block;
							}
						}
					}
				}
				.guideUl{
					background: #fff;
					font-size:0;
					li{
						border-bottom:1px solid #eee;
						display:inline-block;
						line-height:40px;
						font-size:14px;
						width:100%;
						padding:0 10px;
						color:#333;
						&.cur{
							color: #00DC82;
							font-weight:700;
						}
						&:last-child{
							border-bottom:none;
						}
						span{
							display:inline-block;
							width:15px;
							height:10px;
							border-left:2px solid #00DC82;
							border-bottom:2px solid #00DC82;
							transform:translateY(13px) rotate(-45deg);
							-webkit-transform:translateY(13px) rotate(-45deg);
							float:right;
							display:none;
						}
						&:first-child{
							span{
								display:block;
							}
						}
					}
				}
			}
		}
	}
	.pharmacy-list{
		.pharmacy-item{
			padding:9px 10px 8px 15px;
			background:#fff;
			margin-top: 10px;
			display:flex;
			display:-webkit-flex;
			position:relative;
			.video-mesg{
				width:55%;
				height:90px;
				display:flex;
				display:-webkit-flex;
				flex-direction:column;
				-webkit-flex-direction:column;
				justify-content:space-between;
				-webkit-justify-content:space-between;
				.title{
					font-size:16px;
					line-height:22px;
					color:#333;
					font-weight:700;
					overflow:hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient:vertical;
					word-break:break-all;
					white-space:normal;
					height:46px;
				}
				.time-watch{
					font-size:12px;
					line-height:16px;
					color:#999;
					.time{
						float:left;
					}
					.watchtimes{
						float:right;
						padding:0 10px 0 20px;
						background:url('./images/eye.png') left 2px no-repeat;
						background-size:25% 70%;
					}
				}
			}
			.img-box{
				flex:1;
				-webkit-flex:1;
				margin-left:20px;
				position:relative;
				height:90px;
				img{
					border-radius:4px;
					height:90px;
				}
				a{
					display:inline-block;
					position:absolute;
					z-index:1;
					left:0;
					top:0;
					width:100%;
					height:100%;
					border-radius:4px;
					background:url('./images/play.png') center center no-repeat;	
					background-size:18%;
					// background-color:rgba(250,250,250,0.5);
				}
			} 
			.linka{
				display:block;
				width:100%;
				height:100%;
				position:absolute;
				left:0;
				top:0;
				height:100px;
				z-index:2;
			}
		}
	}
}
@media(max-width:480px) {
	#page > .content > .pharmacy .chu-box-da .pharmacy-cont .pharmacy-item .photo img {
	
	}
}
