#page>.content>.onlinepay{
	overflow-y:auto; 
	border-top: none;
	background: #fff;
	.linepay-loading{
		text-align: center;
		padding-top: 50px;
		font-size: 16px;
	}
	.linepay{
		display: none;
	}
	.main{
		background: #fff;
		padding: 10px 5%;
		width: 90%;
		clear: both;
	}
	.title {
	    font-weight: 500px;
	    font-size: 16px;
	    margin-top: 20px;
	    height: 35px;
	    line-height: 35px;
	    background: #f2f2f2;
	    padding-left: 15px;
	    color: #333;
	}
	.text{
		background: #fff;
		padding: 10px;
	}
	.cl{
		color: #bdeedd;
		display: inline-block;
		width: 8%;
		float: left;
		margin-top: 4px;
	}
	.rt{
		display: inline-block;
		float: left;
		width: 92%;
		line-height: 24px;
	}
	.phone{
		text-align: center;
		margin-top: 30px;
		margin-bottom: 30px;
	}
	.phone a{
		color: #31cb96;
		text-decoration: none;
	}
	.clear{
		display: inline-block;
		clear: both;
		padding: 3px 0;

	}
}