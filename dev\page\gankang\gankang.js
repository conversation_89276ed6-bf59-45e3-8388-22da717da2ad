'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.gankang");
	$('body').off('touchmove');

		$(".gankang-cancle .add").unbind();
		$(".gankang-cancle .add").bind("click",function(){
			var addProNum=parseInt($(this).attr("data-addNum"));
			var isSplit=$(this).attr("data-split");
				/*if(isSplit==1){
					addProNum=10;
				}*/
			var total = $(this).next(".inp-total");
			var minBtn=total.next('.min');
			var proid=total.attr("data-id");
			var totalVal=parseInt(total.attr("data-val"));
			total.attr("data-val",parseInt(totalVal+addProNum));
			totalVal=total.attr("data-val");

			$.ajax({
				type: "POST",
				url: apiUrl+"/app/changeCart",
				headers : {'version':version,'terminalType':terminalType},
				data: {"amount":totalVal,"skuId":proid,"merchantId":merchantId},
				dataType: "json",
				success: function(data) {
					if(data.status=="success"){
						if(data.data.qty>0){
							if(data.dialog!=null){
								showHint(data.dialog.msg);
							}
							total.val(data.data.qty);
							total.attr("data-val",data.data.qty);
							
							try{
					   	    	window.webkit.messageHandlers.addPlanNumber.postMessage({proid:proid,pronum:data.data.qty,isAdd:1});//ios
					    	}catch(erro){

					    	};
					    	try{
					   	    	window.hybrid.addPlanNumber(proid,data.data.qty,1); //android
					    	}catch(erro){

					    	};
					    	
						}else{
							total.val(data.data.qty);
							total.attr("data-val",data.data.qty);
							showHint(data.dialog.msg);
						}
					}else{
						if(data.msg){
								total.val(0);
								total.attr("data-val",0);
								showHint(data.msg); 
							}else{
								showHint(data.dialog.msg); 
							}
					}
				}
			});
			
		});
		$(".gankang-cancle .min").unbind();
		$(".gankang-cancle .min").bind('click',function(){
			var minProNum=parseInt($(this).attr("data-minNum"));
			var isSplit=$(this).attr("data-split");
				if(isSplit==1){
					minProNum=1;
				}
			var total=$(this).prev('.inp-total'); 
			var proid=total.attr("data-id");
			var totalVal=parseInt(total.attr("data-val"));
			
			totalVal=parseInt(totalVal-minProNum);
			if(totalVal%minProNum!=0){
				if(isSplit==0){
					showHint("只能以"+minProNum+"的倍数购买");
				}
			}
			if(totalVal<=0){ 
				$.ajax({
					type: "POST",
					url: apiUrl+"/app/batchRemoveProductFromCart",
					headers : {'version':version,'terminalType':terminalType},
					data: {"merchantId":merchantId,"ids":proid},
					dataType: "json",
					success: function(data) {
						if(data.status=="success"){
							total.val(0);
							total.attr("data-val","0");
							try{
					   	    	window.webkit.messageHandlers.addPlanNumber.postMessage({proid:proid,pronum:"0",isAdd:2});//ios
					    	}catch(erro){

					    	};
					    	try{
					   	    	window.hybrid.addPlanNumber(proid,"0",2); //android
					    	}catch(erro){

					    	};
						}
					}
				});
				
			} else{
				total.attr("data-val",totalVal);
				total.val(parseInt(total.val())-minProNum);
				$.ajax({
					type: "POST",
					url: apiUrl+"/app/changeCart",
					headers : {'version':version,'terminalType':terminalType},
					data: {"amount":total.val(),"skuId":proid,"merchantId":merchantId},
					dataType: "json",
					success: function(data) {
						if(data.status=="success"){
							total.val(data.data.qty);
							total.attr("data-val",data.data.qty);
							
							try{
					   	    	window.webkit.messageHandlers.addPlanNumber.postMessage({proid:proid,pronum:data.data.qty,isAdd:2});//ios
					    	}catch(erro){

					    	};
					    	try{
					   	    	window.hybrid.addPlanNumber(proid,data.data.qty,2); //android
					    	}catch(erro){

					    	};
						}else{
							if(data.msg){
								showHint(data.msg); 
							}else{
								showHint(data.dialog.msg); 
							}
						}
					}
				});
			}
			
		});
		$(".gankang-cancle .inp-total").unbind();
		$(".gankang-cancle .inp-total").bind('change',function(){
			var total=$(this);
			var proid=total.attr("data-id");
			var inpProNum=parseInt($(this).attr("data-inpNum"));
			var issplit=$(this).attr("data-isSplit");
			if(total.val()=="" || total.val()<=0 ){
				var totalVal=0;
				$.ajax({
					type: "POST",
					url: apiUrl+"/app/batchRemoveProductFromCart",
					headers : {'version':version,'terminalType':terminalType},
					data: {"merchantId":merchantId,"ids":proid},
					dataType: "json",
					success: function(data) {
						if(data.status=="success"){
							total.val(0);
							total.attr("data-val","0");
						
							try{
					   	    	window.webkit.messageHandlers.addPlanNumber.postMessage({proid:proid,pronum:"0",isAdd:1});//ios
					    	}catch(erro){

					    	};
					    	try{
					   	    	window.hybrid.addPlanNumber(proid,"0",1); //android
					    	}catch(erro){

					    	};
						}
					}
				});
				return false;
			}else{
				if(total.val()%inpProNum==0){
					var totalVal=parseInt(total.val());
				}else{
					if (issplit==0) {
						showHint("只能以"+inpProNum+"的倍数购买");
					}
					var totalVal=parseInt(total.val());
				}
				
			};
			
			total.val(totalVal);
			$.ajax({
				type: "POST",
				url: apiUrl+"/app/changeCart",
				headers : {'version':version,'terminalType':terminalType},
				data: {"amount":totalVal,"skuId":proid,"merchantId":merchantId},
				dataType: "json",
				success: function(data) {
					if(data.status=="success"){
						
						if(data.data.qty>0){
							if(data.dialog!=null){
								showHint(data.dialog.msg);
							}
							total.val(data.data.qty);
							total.attr("data-val",data.data.qty);
							try{
					   	    	window.webkit.messageHandlers.addPlanNumber.postMessage({proid:proid,pronum:data.data.qty,isAdd:1});//ios
					    	}catch(erro){

					    	};
					    	try{
					   	    	window.hybrid.addPlanNumber(proid,data.data.qty,1); //android
					    	}catch(erro){

					    	};
						}else{
							total.val(data.data.qty);
							total.attr("data-val",data.data.qty);
							showHint(data.dialog.msg);
						}
					}else{
						total.val("0");
						if(data.msg){
								showHint(data.msg); 
							}else{
								showHint(data.dialog.msg); 
							}
					}
				}
			});
		});

		
		if(browser.name==="IOS"){
			
		}else{
			$(".gankang-cancle .inp-total").attr("readonly", "true");
			$(".gankang-cancle .inp-total").bind("click", function(){
				var proid = $(this).attr("data-id");
				var pronum = $(this).val();
				var inpProNum=parseInt($(this).attr("data-inpNum"));
				var isSplit = $(this).attr("data-isSplit");

				try{
		   	    	window.hybrid.showKeyboard(proid, pronum, inpProNum, isSplit); //android
		    	}catch(erro){

		    	};
			});
		
			
		};

		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=W_T_G_K&sort=1&limit=10",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId},
			dataType: "json",
			success: function(res) {
				var gankangTemp = " ";
				var gankangTarget = $(".gankang-cont");
				
				for(var i=0; i<res.data.rows.length; i++){
					if(res.data.rows[i].id == 450){
						$(".inp-total-450").val(res.data.rows[i].cartProductNum);
						$(".inp-total-450").attr("data-val",res.data.rows[i].cartProductNum)
					}else if(res.data.rows[i].id == 592){
						$(".inp-total-592").val(res.data.rows[i].cartProductNum);
						$(".inp-total-592").attr("data-val",res.data.rows[i].cartProductNum)
					}
				}
			}
		})
		
	
	return {
		onload: function(force) {
			setAppTitle("吴太感康专场")
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});