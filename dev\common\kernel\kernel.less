@import "../../config.less";
* {
    font-family: @defaultfont;
    box-sizing: border-box;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    -webkit-touch-callout: none;
    font-size: inherit;
    line-height: 1.2;
    -webkit-text-size-adjust: none;
    -moz-text-size-adjust: none;
    -ms-text-size-adjust: none;
    text-size-adjust: none;
}
html {
    width: 100%;
    height: 100%;
    -ms-user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    font-size: 14px;
}
body {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;

    &.clean {
        #page {
            > .navMenu,
            > .header {
                display: none;
            }
        }
    }

    &.mask {
        > #popup,
        > #readable,
        > #page {
            -webkit-filter: blur(5px);
            filter: blur(5px);
        }
    }
}
a {
    outline: none;
    text-decoration: none;
}
i{
    font-style:normal;
}

#page,
#popup {
    > .header {
        position: relative;
        height: 40px;
        width: 100%;
        color: @headerText;
        background-color: @headerBackground;
        .flexcontent(0);
        > .back {
            display: block;
            position: absolute;
            top: 12px;
            left: 0;
            font-size: 16px;
            line-height: 1;
            height: 16px;
            color: @headerText;
            > svg {
                height: 100%;
                vertical-align: top;
                fill: @headerText;
                margin:0 4px;
            }
        }
        > .title {
            font-weight: bold;
            font-size: 18px;
            width: 100%;
            height: 40px;
            line-height: 40px;
            text-align: center;
        }
    }
    > .content {
        position: relative;
        width: 100%;
        background-color: @appBackground;
        .flexcontent(1);
        > div {
            position: absolute;
            width: 100%;
            height: 100%;
            right: 100%;
            visibility: hidden;
            color: @appText;
            border-top: 1px solid @headerBorder;
            -webkit-animation-duration: 400ms;
            animation-duration: 400ms;
            -webkit-animation-timing-function: ease-in-out;
            animation-timing-function: ease-in-out;
            -webkit-animation-iteration-count: 1;
            animation-iteration-count: 1;
        }
    }
}
#page {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    .flexbox(1);
    > .header {
        > .rightMenuBtn,
        > .leftMenuBtn {
            display: block;
            position: absolute;
            top: 0;
            bottom: 0;
            height: 16px;
            font-size: 16px;
            line-height: 1;
            margin: auto 0;
            color: @headerText;
            > svg {
                height: 100%;
                fill: @headerText
            }
        }
        > .leftMenuBtn {
            left: 10px;
        }
        > .rightMenuBtn {
            right: 10px;
        }
    }
    > .navMenu {
        height: 49px;
        background-color: @footerBackground;
        position: relative;
        box-shadow: 0 0 5px @footerText;
        .flexbox(0);
        .flexcontent(0);
        > a {
            display: block;
            height: 100%;
            width: 100%;
            text-align: center;
            color: @footerText;
            font-size: 12px;
            .flexcontent(1);
            > svg {
                display: block;
                height: 20px;
                margin: 5px auto;
                fill: @footerText;
            }
            &.selected {
                color: @themeColor;
                > svg {
                    fill: @themeColor;
                }
            }
        }
    }
}
#popup {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 100%;
    height: 100%;
    visibility: hidden;
    .flexbox(1);
    > .header > .close {
        display: block;
        position: absolute;
        width: 20px;
        height: 20px;
        top: 0;
        bottom: 0;
        right: 10px;
        margin: auto 0;
        > svg {
            width: 100%;
            height: 100%;
            fill: @headerText;
        }
    }
}

#dialog,
#loading,
#photoView {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    visibility: hidden;
    background-color: rgba(0,0,0,0.1);
}
#dialog > div {
    max-width: 85%;
    min-width: 240px;
    max-height: 90%;
    > .content {
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
        text-align: center;
    }
    > .btns {
        text-align: center;
        margin-bottom: 20px;
        > a {
            display: inline-block;
            border-radius: 4px;
            line-height: 24px;
            width: 50px;
            margin: 0 15px;
            vertical-align: top;
            border-width: 1px;
            border-style: solid;
            &.yes {
                color: @primaryFrontground;
                background-color: @primaryBackground;
                border-color: @primaryBorder;
            }
            &.no {
                color: @defaultFrontground;
                background-color: @defaultBackground;
                border-color: @defaultBorder;
            }
        }
    }
    > .close {
        position: absolute;
        right: 5px;
        top: 5px;
        width: 20px;
        height: 20px;
        background: url(./images/close.png) no-repeat;
        background-size: 100%;
    }
    &.alert > .content {
        margin: 20px 10px;
    }
    &.confirm > .content {
        margin: 16px 20px;
    }
    &.alert > .btns,
    &.confirm > .close,
    &.htmlDialog > .btns {
        display: none;
    }
}
#dialog > div,
#loading > div,
#toast > div {
    position: absolute;
    margin: auto;
    right: 0;
    left: 0;
    bottom: 0;
    top: 0;
    border-radius: 5px;
    // background-color: rgba(40,40,40,0.75);
    background-color: #34384A;
    box-shadow: 0 0 5px gray;
    color: #fff;
}
#loading > div {
    width: 120px;
    height: 120px;
    text-align: center;
    font-size: 16px;
    line-height: 1;
    white-space: pre-wrap;
    > .ico {
        margin: 30px auto 15px auto;
        width: 38px;
        height: 38px;
        vertical-align: baseline;
    -webkit-animation: weuiLoading 1s steps(12, end) infinite;
    animation: weuiLoading 1s steps(12, end) infinite;
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;
    background-size: 100%;
       
    }
}
#toast > div{
    width: 120px;
    height: 120px;
    text-align: center;
    font-size: 16px;
    line-height: 1;
    white-space: pre-wrap;
    z-index: 10;
    > .ico{
        margin: 30px auto 15px auto;
        width: 38px;
        height: 38px;
        vertical-align: baseline;
        background: url("./images/<EMAIL>") no-repeat center top;
        background-size: 100%;
    }
}
#sureDialog{
    background-color: rgba(0, 0, 0, 0.3);
        position: absolute;
        margin: auto;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
    .surecont{
        width: 75%;
        height: 130px;
        z-index: 10;
        position: absolute;
        margin: auto;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        background: #fff;
        border-radius: 8px;
        .sure-text{
            text-align: center;
            font-size: 14px;
            line-height: 1.5;
            height: 95px;
            color: #000;
            font-weight: bold;
            padding:20px 10px;
            border-bottom: 1px solid #eee;
        }
        .sure-btn{
            text-align: center;
            line-height: 34px;
            color: #30b6ff;
            font-weight: bold;
        }
    }
}
@-webkit-keyframes loadingAni {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes loadingAni {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
#popup.in,
#readable.in {
    bottom: 0;
    visibility: visible;
    -webkit-animation: popupin 400ms 1 ease-in-out;
    animation: popupin 400ms 1 ease-in-out;
}
#popup.out,
#readable.out {
    visibility: visible;
    -webkit-animation: popupout 400ms 1 ease-in-out;
    animation: popupout 400ms 1 ease-in-out;
}
@-webkit-keyframes popupin {
    from {
        bottom: -100%;
    }
    to {
        bottom: 0;
    }
}
@keyframes popupin {
    from {
        bottom: -100%;
    }
    to {
        bottom: 0;
    }
}
@-webkit-keyframes popupout {
    from {
        bottom: 0;
    }
    to {
        bottom: -100%;
    }
}
@keyframes popupout {
    from {
        bottom: 0;
    }
    to {
        bottom: -100%;
    }
}
.panelTransL1 {
    -webkit-animation-name: panelTransL1;
    animation-name: panelTransL1;
}
.panelTransL2 {
    -webkit-animation-name: panelTransL2;
    animation-name: panelTransL2;
}
.panelTransR1 {
    -webkit-animation-name: panelTransR1;
    animation-name: panelTransR1;
}
.panelTransR2 {
    -webkit-animation-name: panelTransR2;
    animation-name: panelTransR2;
}
@-webkit-keyframes panelTransL1 {
    from {
        right: 0;
    }
    to {
        right: 100%;
    }
}
@keyframes panelTransL1 {
    from {
        right: 0;
    }
    to {
        right: 100%;
    }
}
@-webkit-keyframes panelTransR1 {
    from {
        right: 0;
    }
    to {
        right: -100%;
    }
}
@keyframes panelTransR1 {
    from {
        right: 0;
    }
    to {
        right: -100%;
    }
}
@-webkit-keyframes panelTransL2 {
    from {
        right: -100%;
    }
    to {
        right: 0;
    }
}
@keyframes panelTransL2 {
    from {
        right: -100%;
    }
    to {
        right: 0;
    }
}
@-webkit-keyframes panelTransR2 {
    from {
        right: 100%;
    }
    to {
        right: 0;
    }
}
@keyframes panelTransR2 {
    from {
        right: 100%;
    }
    to {
        right: 0;
    }
}
#photoView {
    > .content {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        .item {
            background-position: 50% 50%;
            background-repeat: no-repeat;
            background-size: contain;
        }
    }
    > .nav {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 10px;
        text-align: center;
        pointer-events: none;
        color: @themeColor;
        font-family: Serif;
        font-size: 18px;
        text-shadow: 0 0 1px silver;
    }
}
#photoView,
#readable {
    > .close {
        position: absolute;
        right: 4px;
        top: 4px;
        width: 32px;
        height: 32px;
        border-radius: 16px;
        display: block;
        background-color: white;
        box-shadow: 0 0 4px gray inset;
        > svg {
            width: 16px;
            height: 16px;
            margin: 8px;
            fill: gray;
        }
    }
}
#readable {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 100%;
    height: 100%;
    visibility: hidden;
    overflow-y: auto;
    background-color: white;
    > .content {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow-y: auto;
    }
}
#hint {
    position: absolute;
    left: 20px;
    right: 20px;
    pointer-events: none;
    text-align: center;
    opacity: 0;
    bottom: 200px;
    font-size: 14px;
    z-index:10;
    -webkit-transition: opacity 400ms ease-in-out;
    transition: opacity 400ms ease-in-out;
    > .text {
        display: inline-block;
        color: @appBackground;
        line-height: 20px;
        padding: 8px 12px;
        background-color: rgba(0,0,0,0.7);
        border-radius: 5px;
        line-height: 20px;
        padding: 5px 12px;
        box-shadow: 0 0 3px silver;
        margin: 3px;
        white-space: pre-wrap;
        vertical-align: top;
    }
}
#helper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    > div {
        width: 100%;
        height: 100%;
        -webkit-tap-highlight-color: transparent;
        .flexbox(1);
        > div {
            .flexbox(0);
            > div {
                height: 100%;
            }
            &:first-child,
            &:last-child,
            > div:first-child,
            > div:last-child {
                background-color: rgba(0,0,0,0.7);
            }
        }
    }
    > img {
        position: absolute;
    }
}
.hScroll {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}
.iosScrollFix {
    padding-top: 1px;
    padding-bottom: 1px;
    -webkit-overflow-scrolling: touch;
}
.iosScrollFix:before {
    content: "";
    display: block;
    position: absolute;
    bottom: -2px;
    right: 0;
    height: 1px;
    width: 1px;
    visibility: hidden;
    overflow: hidden;
    pointer-events: none;
}
div.reloadHint {
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 40px;
    height: 40px;
    opacity: 0;
    background-color: white;
    padding: 8px;
    box-shadow: 0 0 4px gray;
    border-radius: 20px;
    > svg {
        display: block;
        height: 100%;
        fill: @primaryBackground;
        -webkit-transition: fill 200ms ease-in-out;
        transition: fill 200ms ease-in-out;
    }
    &.pin > svg {
        fill: @successBackground;
    }
}


@-webkit-keyframes weuiLoading {
    0% {
        transform: rotate3d(0, 0, 1, 0deg);
    }

    100% {
        transform: rotate3d(0, 0, 1, 360deg);
    }
}

@keyframes weuiLoading {
    0% {
        transform: rotate3d(0, 0, 1, 0deg);
    }

    100% {
        transform: rotate3d(0, 0, 1, 360deg);
    }
}
