'use strict';
define(['module', 'common/kernel/kernel', 'common/zepto/zepto'], function(module, kernel, zepto) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);

		
if(browser.name==="IOS"){
	$(".link1").attr("href","ybmpage://commonh5activity?url=/temp/coinReule.html&ybm_title=余额使用说明");
	$(".link2").attr("href","ybmpage://commonh5activity?url=#!newberb&ybm_title=619周年庆");
		
}else{
	$(".link1").attr("href","ybmpage://commonh5activity?url="+baseUrl+"/temp/coinReule.html");
	$(".link2").attr("href","ybmpage://commonh5activity?url="+baseUrl+"/?ybm_title=619周年庆&cache=0#!newberb");
	
};
	return {
		onload: function(force) {
			
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});