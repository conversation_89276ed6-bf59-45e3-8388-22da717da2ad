#page.home{
    .search{
        display: block;
    }
}
#page>.content>.home {
    background-color: #f4f4f4;
    border-top: none;
    overflow-y: auto;
   
    .tentrance{
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        width: 100%;
        height: 120px;
        background: #fff;
        margin-bottom: 10px;
        .tentrance-item{
            display: block;
            -webkit-box-flex: 1;
            -ms-flex: 1 1 auto;
            -webkit-flex: 1 1 auto;
            flex: 1 1 auto;
            height: 110px;
            text-align: center;
            padding: 18px 0;
            .tentrance-photo{
                width: 60px;
                height: 60px;
                display: inline-block;
                img{
                    display: block;
                    width: 100%;
                }
            }
           
            .tentrance-name{
                color: #666;
                padding-top: 6px;
            }
        }
    }
    .home-ads{
        margin-bottom: 10px;
        img{
            display: block;
            width: 100%;
        }
        &.home-ads4{
            margin-top: -10px;
        }
        &.home-ads1{
            margin-top: 10px;
        }
    }
    .notice{
        height: 45px;
        background: #fff;
        padding: 0 10px;
        .notice-icon{
            display: inline-block;
            width: 20%;
            height: 45px;
            background: url(./images/icon-notice.png) no-repeat;
            background-size: 100%;
            float: left;
            margin-top: 12px;
        }
        .notice-list{
            width: 78%;
            float: left;
            height: 45px;
            padding-left: 2%;
            position: relative;
            .notice-item{
                display: block;
                width: 100%;
                height: 45px;
                color: #333;
                line-height: 42px;
                transition: all 0.3s ease;
                font-size: 12px;
                padding-top: 5px;
                padding-left: 5px;
                p{
                    font-size: 12px;
                    line-height: 18px;
                    padding: 0;
                    margin: 0;
                }
            }
            .notice-scoll{
                height: auto;
                width: 100%;
                position: absolute;
                transition: all 0.3s ease,
            }
        }
    }
    .trushbuy{
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        margin-bottom: 10px;
        .trushbuy-item{
            -webkit-box-flex: 1;
            -ms-flex: 1 1 auto;
            -webkit-flex: 1 1 auto;
            flex: 1 1 auto;
            img{
                display: block;
                width: 100%;
            }
        }
        
    }
   
  
    .hot-title{
        width: 36%;
        height: 27px;
        margin: 0 auto;
    }
   
   .hot-pro5{
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        .hot-pro-item{
            -webkit-box-flex: 1;
            -ms-flex: 1 1 auto;
            -webkit-flex: 1 1 auto;
            flex: 1 1 auto;
        }
    }
    .hot-day{
        background: #fff;
        margin-bottom: 10px;
        margin-top: 10px;
        padding-bottom: 12px;
        .hot-day-title{
            height: 30px;
            .tit{
                display: inline-block;
                width: 85px;
                float: left;
            }
            .more{
                float: right;
                padding-right: 10px;
                line-height: 30px;
                color: #999;
            }
        }
        .hot-day-cont {
            border-bottom: 1px solid #d8d8d8;
            margin-top: 2px;
            .hot-day-item {
                width: 33.33%;
                display: inline-block;
                float: left;
                border: 1px solid #d8d8d8;
                padding: 0 5px;
                margin: 0 -1px -1px 0;
                position: relative;
                img{
                    height: 97px;
                }
                .name {
                    color: #333;
                    font-size: 13px;
                    width: 100%;
                    height: 14px;
                    overflow: hidden;
                }
                .price {
                    font-size: 16px;
                    color: #ff2400;
                    padding: 5px 0;
                }
                .norms {
                    font-size: 12px;
                    color: #999999;
                    text-align: right;
                    padding: 5px 0 10px;
                    height: 20px;
                }
                .gone-icon{
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    background: url(./images/<EMAIL>) center center no-repeat;
                    background-size: 70%;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                }
            }
        }

    }

    .hot-pro{
        margin-bottom: 10px;
        background: #fff;
    }
    
    .hot-list{
        overflow-x:auto;
        padding: 10px;
        .hot-list-scrool1,.hot-list-scrool2,.hot-list-scrool3{
            height: 165px;
            .hot-item-more{
               
                text-align: center;
                line-height: 155px;
            }
        }
        .hot-item{
            position: relative;
            .photo{
                padding: 5px;
                border: 1px solid #dadada;
            }
            display: block;
            float: left;
            width: 100px;
            margin-right: 5px;
            .name{
                padding-top:5px; 
            }
            .price{
                padding: 5px;
                font-size: 16px;
            }
            .norms{
                font-size: 12px;
                color: #999;
                text-align: right;
            }
            .gone-icon{
                        position: absolute;
                        width: 100%;
                        height: 70%;
                        background: url(./images/<EMAIL>) center center no-repeat;
                        background-size: 70%;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                    }
                    .markerUrl{
                        position: absolute;
                        width: 30px;
                        height: 35px;
                        top: -1px;
                        left: 0;
                    }
        }
    }
    
}
@media (max-width:320px) {
    #page > .content > .home .hot-list .hot-item {
        width: 90px;
    }
    #page > .content > .home .hot-list .hot-list-scrool1, #page > .content > .home .hot-list .hot-list-scrool2, #page > .content > .home .hot-list .hot-list-scrool3 {
        height: 155px;
    }
}