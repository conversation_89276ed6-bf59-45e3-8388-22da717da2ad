'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	$('body').off('touchmove');
	var $dom = $("#page>.content>.autumn");
	
	var scrollTop1;
	var scrollTop2;
	var scrollTop3;
	var scrollTop4;
	var scrollTop5;
	var titHtop = $(".autumn-tab").offset().top;
    var tabHeight = $(".autumn-tab").height();
    var descHeight = $(".autumn-desc").height();
    var tabBoxHeight=tabHeight + descHeight;
    $(".autumn-tab-box").css("height",tabBoxHeight);
    
	$(".autumn-top-item1").click(function (){  
        $dom.animate({  
            scrollTop: scrollTop1-tabHeight+2
        }, 0);  
    });
    $(".autumn-top-item2").click(function (){  
        $dom.animate({  
            scrollTop: scrollTop2-tabHeight+3
        }, 0);  
    });
    $(".autumn-top-item3").click(function (){  
        $dom.animate({  
            scrollTop: scrollTop3-tabHeight+4
        }, 0);  
    });
    $(".autumn-top-item4").click(function (){ 
        $dom.animate({  
            scrollTop: scrollTop4-tabHeight+5
        }, 0);  
    });
    $(".autumn-top-item5").click(function (){  
        $dom.animate({  
            scrollTop: scrollTop5-tabHeight+6
        }, 0);  
    });


    $dom.scroll(function(){
    	if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
    	
		var sHtop = $dom.scrollTop();//滚动条距离顶部的距离
		if(sHtop>(titHtop+tabHeight)){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时
			$(".autumn-tab").addClass("fixed");
			
		}else if(sHtop<titHtop+tabHeight){
			$(".autumn-tab").removeClass("fixed");
		};
		sHtop = sHtop+tabHeight;
		if(sHtop>scrollTop1 && sHtop<scrollTop2){
			$(".autumn-tab-org").removeClass("cur1");
			$(".autumn-tab-bt").removeClass("cur2");
			$(".autumn-tab-org1").addClass("cur1");
			$(".autumn-tab-bt1").addClass("cur2");

		}else if(sHtop>scrollTop2 && sHtop<scrollTop3){
			$(".autumn-tab-org").removeClass("cur1");
			$(".autumn-tab-bt").removeClass("cur2");
			$(".autumn-tab-org2").addClass("cur1");
			$(".autumn-tab-bt2").addClass("cur2");
		}else if(sHtop>scrollTop3 && sHtop<scrollTop4){
			$(".autumn-tab-org").removeClass("cur1");
			$(".autumn-tab-bt").removeClass("cur2");
			$(".autumn-tab-org3").addClass("cur1");
			$(".autumn-tab-bt3").addClass("cur2");
		}else if(sHtop>scrollTop4 && sHtop<scrollTop5){
			$(".autumn-tab-org").removeClass("cur1");
			$(".autumn-tab-bt").removeClass("cur2");
			$(".autumn-tab-org4").addClass("cur1");
			$(".autumn-tab-bt4").addClass("cur2");
		}else if(sHtop>scrollTop5 ){
			$(".autumn-tab-org").removeClass("cur1");
			$(".autumn-tab-bt").removeClass("cur2");
			$(".autumn-tab-org5").addClass("cur1");
			$(".autumn-tab-bt5").addClass("cur2");
		}
	});

	$.ajax({
		type: "POST",
		async: "false",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=zb_gm&sort=1&limit=4",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var autumnTempGross = " ";
			var autumnTargetGross = $(".autumn-gross");
			
			for(var i=0;i<res.data.rows.length;i++){
				autumnTempGross+='<div class="autumn-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					autumnTempGross += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							autumnTempGross += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							autumnTempGross += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							autumnTempGross += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					autumnTempGross+='</div></a>'
				}
				autumnTempGross+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							autumnTempGross+='<div class="dujia">独家</div>'
						}
					autumnTempGross+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				autumnTempGross+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				autumnTempGross+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:autumnTempGross+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:autumnTempGross+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:autumnTempGross+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:autumnTempGross+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				autumnTempGross+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									autumnTempGross+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										autumnTempGross+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										autumnTempGross+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// autumnTempGross+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										autumnTempGross+='<div class="zhonbao-des"></div>';
									}*/
										autumnTempGross+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								autumnTempGross+='<div class="price">暂无购买权限</div>';
								autumnTempGross += '<div class="control-box"></div>';
								// autumnTempGross+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								autumnTempGross+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							autumnTempGross+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								autumnTempGross+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								autumnTempGross+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// autumnTempGross+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							autumnTempGross+='<div class="zhonbao-des"></div>';
						}*/
							autumnTempGross+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						autumnTempGross+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						autumnTempGross+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				autumnTempGross+='</div>';
			}
			autumnTargetGross.empty().append(autumnTempGross);
			
			rowAddMin();
		
			
		}
	});
		
	$.ajax({
		type: "POST",
		async: "false",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=zb_bx&sort=1&limit=10",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var autumnTemp1 = " ";
			var autumnTarget1 = $(".autumn-cont1");
			
			for(var i=0;i<res.data.rows.length;i++){
				autumnTemp1+='<div class="autumn-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					autumnTemp1 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							autumnTemp1 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							autumnTemp1 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							autumnTemp1 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					autumnTemp1+='</div></a>'
				}
				autumnTemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							autumnTemp1+='<div class="dujia">独家</div>'
						}
					autumnTemp1+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				autumnTemp1+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				autumnTemp1+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:autumnTemp1+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:autumnTemp1+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:autumnTemp1+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:autumnTemp1+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				autumnTemp1+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									autumnTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										autumnTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										autumnTemp1+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// autumnTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										autumnTemp1+='<div class="zhonbao-des"></div>';
									}*/
										autumnTemp1+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								autumnTemp1+='<div class="price">暂无购买权限</div>';
								autumnTemp1 += '<div class="control-box"></div>';
								// autumnTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								autumnTemp1+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							autumnTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								autumnTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								autumnTemp1+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// autumnTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							autumnTemp1+='<div class="zhonbao-des"></div>';
						}*/
							autumnTemp1+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						autumnTemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						autumnTemp1+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				autumnTemp1+='</div>';
			}
			autumnTarget1.append(autumnTemp1);
			scrollTop1 = $(".autumn-title1").offset().top;

			
		rowAddMin();
		
			
		}
	});
		
	$.ajax({
		type: "POST",
		async: "false",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=zb_qs&sort=1&limit=10",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var autumnTemp2 = " ";
			var autumnTarget2 = $(".autumn-cont2");
			for(var i=0;i<res.data.rows.length;i++){
				autumnTemp2+='<div class="autumn-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					autumnTemp2 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							autumnTemp2 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							autumnTemp2 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							autumnTemp2 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					autumnTemp2+='</div></a>'
				}
				autumnTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							autumnTemp2+='<div class="dujia">独家</div>'
						}
					autumnTemp2+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				autumnTemp2+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				autumnTemp2+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:autumnTemp2+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:autumnTemp2+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:autumnTemp2+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:autumnTemp2+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				autumnTemp2+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									autumnTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										autumnTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										autumnTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// autumnTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										autumnTemp2+='<div class="zhonbao-des"></div>';
									}*/
										autumnTemp2+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								autumnTemp2+='<div class="price">暂无购买权限</div>';
								autumnTemp2 += '<div class="control-box"></div>';
								// autumnTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								autumnTemp2+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							autumnTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								autumnTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								autumnTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// autumnTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							autumnTemp2+='<div class="zhonbao-des"></div>';
						}*/
							autumnTemp2+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						autumnTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						autumnTemp2+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				autumnTemp2+='</div>';
			}
			autumnTarget2.append(autumnTemp2);
			scrollTop2 = $(".autumn-title2").offset().top;

			rowAddMin();
		
			
		}
	});
	
	$.ajax({
		type: "POST",
		async: "false",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=zb_wk&sort=1&limit=10",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var autumnTemp3 = " ";
			var autumnTarget3 = $(".autumn-cont3");
			for(var i=0;i<res.data.rows.length;i++){
				autumnTemp3+='<div class="autumn-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					autumnTemp3 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							autumnTemp3 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							autumnTemp3 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							autumnTemp3 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					autumnTemp3+='</div></a>'
				}
				autumnTemp3+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							autumnTemp3+='<div class="dujia">独家</div>'
						}
					autumnTemp3+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				autumnTemp3+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				autumnTemp3+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:autumnTemp3+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:autumnTemp3+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:autumnTemp3+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:autumnTemp3+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				autumnTemp3+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									autumnTemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										autumnTemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										autumnTemp3+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// autumnTemp3+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										autumnTemp3+='<div class="zhonbao-des"></div>';
									}*/
										autumnTemp3+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								autumnTemp3+='<div class="price">暂无购买权限</div>';
								autumnTemp3 += '<div class="control-box"></div>';
								// autumnTemp3+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								autumnTemp3+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							autumnTemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								autumnTemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								autumnTemp3+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// autumnTemp3+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							autumnTemp3+='<div class="zhonbao-des"></div>';
						}*/
							autumnTemp3+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						autumnTemp3+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						autumnTemp3+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				autumnTemp3+='</div>';
			}
			autumnTarget3.append(autumnTemp3);
			scrollTop3 = $(".autumn-title3").offset().top;

			rowAddMin();
		
			
		}
	});
	
	$.ajax({
		type: "POST",
		async: "false",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=zb_as&sort=1&limit=10",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var autumnTemp4 = " ";
			var autumnTarget4 = $(".autumn-cont4");
			for(var i=0;i<res.data.rows.length;i++){
				autumnTemp4+='<div class="autumn-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					autumnTemp4 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							autumnTemp4 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							autumnTemp4 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							autumnTemp4 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					autumnTemp4+='</div></a>'
				}
				autumnTemp4+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							autumnTemp4+='<div class="dujia">独家</div>'
						}
					autumnTemp4+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				autumnTemp4+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				autumnTemp4+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:autumnTemp4+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:autumnTemp4+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:autumnTemp4+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:autumnTemp4+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				autumnTemp4+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									autumnTemp4+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										autumnTemp4+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										autumnTemp4+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// autumnTemp4+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										autumnTemp4+='<div class="zhonbao-des"></div>';
									}*/
										autumnTemp4+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								autumnTemp4+='<div class="price">暂无购买权限</div>';
								autumnTemp4 += '<div class="control-box"></div>';
								// autumnTemp4+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								autumnTemp4+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							autumnTemp4+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								autumnTemp4+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								autumnTemp4+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// autumnTemp4+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							autumnTemp4+='<div class="zhonbao-des"></div>';
						}*/
							autumnTemp4+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						autumnTemp4+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						autumnTemp4+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				autumnTemp4+='</div>';
			}
			autumnTarget4.append(autumnTemp4);
			scrollTop4 = $(".autumn-title4").offset().top;

			rowAddMin();
		
			
		}
	});

	$.ajax({
		type: "POST",
		async: "false",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=zb_bj&sort=1&limit=10",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var autumnTemp5 = " ";
			var autumnTarget5 = $(".autumn-cont5");
			for(var i=0;i<res.data.rows.length;i++){
				autumnTemp5+='<div class="autumn-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					autumnTemp5 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							autumnTemp5 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							autumnTemp5 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							autumnTemp5 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					autumnTemp5+='</div></a>'
				}
				autumnTemp5+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							autumnTemp5+='<div class="dujia">独家</div>'
						}
					autumnTemp5+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				autumnTemp5+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				autumnTemp5+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:autumnTemp5+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:autumnTemp5+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:autumnTemp5+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:autumnTemp5+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				autumnTemp5+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									autumnTemp5+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										autumnTemp5+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										autumnTemp5+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// autumnTemp5+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										autumnTemp5+='<div class="zhonbao-des"></div>';
									}*/
										autumnTemp5+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								autumnTemp5+='<div class="price">暂无购买权限</div>';
								autumnTemp5 += '<div class="control-box"></div>';
								// autumnTemp5+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								autumnTemp5+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							autumnTemp5+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								autumnTemp5+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								autumnTemp5+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// autumnTemp5+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							autumnTemp5+='<div class="zhonbao-des"></div>';
						}*/
							autumnTemp5+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						autumnTemp5+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						autumnTemp5+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				autumnTemp5+='</div>';
			}
			autumnTarget5.append(autumnTemp5);
			scrollTop5 = $(".autumn-title5").offset().top;

			rowAddMin();
		
			
		}
	});
		
	backTop(thispage);

	return {
		onload: function(force) {
			setAppTitle('滋补保健专场')
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});