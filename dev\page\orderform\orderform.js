'use strict';
define(['module', 'common/kernel/kernel', 'common/zepto/zepto'], function(module, kernel, zepto) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.orderform");
	$('body').off('touchmove');

	var orderId;

	function getDataList(){
		$.ajax({
			type: "POST",
			url: apiUrl+"/order/share/list?sort=1",
			headers : {'version':version,'terminalType':terminalType},
			data: {"orderId":orderId},
			dataType: "json",
			success: function(res) {
				if(res.status=="success"){
					var orderformTemp = " ";
					var orderformTarget = $(".orderform-list");
					for(var i in res.order.packageList){
						for(var j=0,resData = res.order.packageList[i].orderDetailList,reslength = resData.length; j<reslength; j++){
							orderformTemp += '<div class="orderform-item">\
											<div class="item-mesg-box">\
												<div class="img-box">\
													<img src="'+hostUrl+'/ybm/product/min/'+resData[j].imageUrl+'" alt="">\
												</div>\
												<div class="med-mesg">\
													<div class="med-name">'+resData[j].productName+'</div>\
													<div class="med-spec">规格:'+resData[j].spec+'</div>\
													<div class="biaoqian-box">'
												if(resData[j].blackProductText != '' && resData[j].blackProductText != null){
													orderformTemp += '<div class="biaoqian normal">'+resData[j].blackProductText+'</div>'
												}
							orderformTemp +='</div>\
												</div>\
												<div class="med-num">X'+resData[j].productAmount+'</div>\
											</div>\
											<div class="item-price-box">\
												<div class="orgin-price-box">\
													<div class="title-box">\
														<div class="title">原单价</div>\
														<div class="token">—</div>\
													</div>\
													<div class="price">￥'+resData[j].productPrice.toFixed(2)+'</div>\
												</div>\
												<div class="orgin-price-box">\
													<div class="title-box">\
														<div class="title">优惠</div>\
														<div class="token">—</div>\
													</div>\
													<div class="price">￥'+(resData[j].discountAmount / resData[j].productAmount).toFixed(2) +'</div>\
												</div>\
												<div class="orgin-price-box">\
													<div class="title-box">\
														<div class="title">返利</div>\
														<div class="token">=</div>\
													</div>\
													<div class="price">￥'+(resData[j].balanceAmount / resData[j].productAmount).toFixed(2)+'</div>\
												</div>\
												<div class="order-price">\
													<div class="title">入库价</div>'
													if(resData[j].purchasePrice != null){
														orderformTemp += '<div class="price">￥'+resData[j].purchasePrice.toFixed(2)+'</div>'
													}else{
														orderformTemp += '<div class="price">暂无数据</div>'
													}
												orderformTemp += '</div>\
											</div>\
										</div>'
						}
					}
					for(var i = 0,dataList = res.order.detailList,datalength=dataList.length; i<datalength; i++){
						orderformTemp += '<div class="orderform-item">\
											<div class="item-mesg-box">\
												<div class="img-box">\
													<img src="'+hostUrl+'/ybm/product/min/'+dataList[i].imageUrl+'" alt="">\
												</div>\
												<div class="med-mesg">\
													<div class="med-name">'+dataList[i].productName+'</div>\
													<div class="med-spec">规格:'+dataList[i].spec+'</div>\
													<div class="biaoqian-box">'
												if(dataList[i].blackProductText != '' && dataList[i].blackProductText != null){
													orderformTemp += '<div class="biaoqian normal">'+dataList[i].blackProductText+'</div>'
												}
							orderformTemp +='</div>\
												</div>\
												<div class="med-num">X'+dataList[i].productAmount+'</div>\
											</div>\
											<div class="item-price-box">\
												<div class="orgin-price-box">\
													<div class="title-box">\
														<div class="title">原单价</div>\
														<div class="token">—</div>\
													</div>\
													<div class="price">￥'+dataList[i].productPrice.toFixed(2)+'</div>\
												</div>\
												<div class="orgin-price-box">\
													<div class="title-box">\
														<div class="title">优惠</div>\
														<div class="token">—</div>\
													</div>\
													<div class="price">￥'+(dataList[i].discountAmount / dataList[i].productAmount).toFixed(2) +'</div>\
												</div>\
												<div class="orgin-price-box">\
													<div class="title-box">\
														<div class="title">返利</div>\
														<div class="token">=</div>\
													</div>\
													<div class="price">￥'+(dataList[i].balanceAmount / dataList[i].productAmount).toFixed(2)+'</div>\
												</div>\
												<div class="order-price">\
													<div class="title">入库价</div>'
													if(dataList[i].purchasePrice != null){
														orderformTemp += '<div class="price">￥'+dataList[i].purchasePrice.toFixed(2)+'</div>'
													}else{
														orderformTemp += '<div class="price">暂无数据</div>'
													}
												orderformTemp += '</div>\
											</div>\
										</div>'
					}

					$(".loading-tips").hide();
					
					orderformTarget.append(orderformTemp);
					$(".nomore-box").show();
				}else if(res.code == 9999){
					$(".loading-tips").hide();
					$(".nodata").show();
				}	
			}
		});
		
	}
	
	
	return {
		onload: function(force) {
			document.title = '入库价';
			orderId = kernel.location.args.orderId;
			$(".nodata").hide();
			getDataList();
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});