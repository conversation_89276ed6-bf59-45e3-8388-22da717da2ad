#page>.content>.newber{
	overflow-y:auto; 
	border-top: none;
	background: #2d14b8;
	color: #f19424;
	.hb-box2{
		width: 100%;
		background: url('/static/public/events/0512/newber33.jpg') center top repeat-y; 
		background-size: 100%;
	}
	.redbao-item{
		width: 75%;
		margin: 0 auto;
		margin-bottom: 15px;
		.redbao-title{
			height: 15px;
			width: 43%;
			text-align: center;
			background: -moz-radial-gradient(#fff, #fff, #fbe1c2);
 			background: -webkit-radial-gradient(#fff, #fff, #fbe1c2);
 			border-radius: 5px 5px 0 0;
 			font-size: 12px;
		}
		.redbao-cont{
			height: 80px;
			.redbao-cont-l{
				width: 75%;
				float: left;
				.redbao-amount{
					display: inline-block;
					width: 30%;
					float: left;
					margin: 20px 0 0 10px;
					.redbao-amount-unt{
						font-size: 16px;
					}
					.redbao-amount-num{
						font-size: 26px;
						font-weight: 400;
					}
				}
				.redbao-plain{
					display: inline-block;
					width: 63%;
					float: left;
					margin-left: 4px;
					.redbao-plain-top{
						display: inline-block;
						font-weight: bold;
						padding: 0 1px;
						background: #f1a124;
						color: #fff;
						margin: 15px 0 0 5px;
					}
					.redbao-plain-cen{
						width: 90%;
						margin-top: 3px;
						margin-bottom: 5px;
					}
					.redbao-plain-bot{
						font-size: 12px;
						padding-left: 5px;
						
					}
				}
				
			}
			.redbao-cont-r{
				width: 25%;
				float: right;
				.redbao-type1{
					font-size: 12px;
					color: #fff;
					.redbao-type-text{
						margin: 14px 0 0 6px;
					}
					.cutday{
						color: #2b2b2b;
					}
					.cutime{
						height: 20px;
					}
					.abo{
						background: #fff;
						color: #333;
						display: inline-block;
						width: 14px;
						height: 14px;
						line-height: 15px;
						text-align: center;
						position: relative;
						top: 2px;
						border-radius: 3px;
						margin: 0 1px;
					}
				}
				.redbao-type2{
					.cro-pross{
						margin:12px auto;
						width:70%;
						position: relative;
						padding-bottom: 10px;
						.cro-pross-text{
							position: absolute;
							width: 100%;
							text-align: center;
							font-size: 10px;
							color: #fdff61;
							padding-top: 5px;
						}
					}
				}
				
			}
		}
	}
	.tips-box{
		background: url('/static/public/events/0512/newber33.jpg') center top repeat-y; 
		background-size: 100%;
		.content-box{
			width:82%;
			margin:0 auto;
			.content-list{
				color:#fff;
				line-height:20px;
				font-size:16px;
				line-height:30px;
				.token{
					width:18px;
					line-height:18px;
					border-radius:50%;
					font-size:14px;
					text-align: center;
					background:#181565;
					display: inline-block;
					margin-right:10px;
					vertical-align:middle;
				}
			}
			table{
				color:#fff;
				width:96%;
				margin:5px auto 0;
				text-align:center;
				font-size:16px;
				td{
					border:1px solid #fff;
					height: 40px;
				}
				th{
					color:#e9cf58;
					border:1px solid #fff;
					height:40px;
					border-bottom:none;
					&:first-child{
						border-right:none;
					}
				}
				.tr-bor{
					td{
						border-bottom: 0;
						&:first-child{
							border-right:0;
						}
					}
				}
				.nobor{
					border-right:0;
				}
			}
			.tips{
				color:#fff;
				font-size:12px;
				line-height:16px;
				margin:10px 0 5px 5px;
			}
		}
	}
	.redbao-item1 .redbao-cont{
		background: url(./images/bg5.jpg) center top no-repeat; 
		background-size: 100%;
	}
	.redbao-item2 .redbao-cont{
		background: url(./images/bg6.jpg) center top no-repeat; 
		background-size: 100%;
	}
	.redbao-item3 .redbao-cont{
		background: url(./images/bg7.jpg) center top no-repeat; 
		background-size: 100%;
	}
	.redbao-item4 .redbao-cont{
		background: url(./images/bg8.jpg) center top no-repeat; 
		background-size: 100%;
	}
}

@media(max-width:320px) {
	#page > .content > .newber .redbao-item .redbao-cont .redbao-cont-l .redbao-amount .redbao-amount-num{
		font-size: 18px;
	}
	#page > .content > .newber .redbao-item .redbao-cont .redbao-cont-l .redbao-plain .redbao-plain-cen{
		margin: 0 0 3px 0;
	}
	#page > .content > .newber .redbao-item .redbao-cont .redbao-cont-r .redbao-type1 .redbao-type-text{
		margin: 8px 0 0 6px;
	}
	#page > .content > .newber .redbao-item .redbao-cont .redbao-cont-r .redbao-type1 .abo{
		margin: 0;
	}
	#page > .content > .newber .redbao-item .redbao-cont .redbao-cont-l .redbao-plain{
		margin-left: 0;
		width: 68%;
	}
	#page > .content > .newber .redbao-item .redbao-cont .redbao-cont-l .redbao-plain .redbao-plain-top{
		margin: 13px 0 0 5px;
	}
	#page > .content > .newber .redbao-item .redbao-cont .redbao-cont-l .redbao-plain .redbao-plain-bot{
		padding-left: 0;
	}
	#page > .content > .newber .redbao-item .redbao-cont .redbao-cont-l .redbao-amount{
		width: 26%;
	}
}
