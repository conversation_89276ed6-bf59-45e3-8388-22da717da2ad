#page > .content > .goldprotemplate {
  border-top: none;
  overflow-y: auto;
}
#page > .content > .goldprotemplate .tab-title {
  display: block;
}
#page > .content > .goldprotemplate .tab-box {
  height: 40px;
  overflow: visible;
}
#page > .content > .goldprotemplate .tab-box .tab-section {
  width: 100%;
  background: #fff;
  position: relative;
  overflow: visible;
  top: 0;
  z-index: 100;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title-arrow {
  display: block;
  width: 40px;
  height: 40px;
  background: #fff url(images/icon-up.png) no-repeat 13px 18px;
  background-size: 35%;
  position: absolute;
  top: 0;
  right: 0;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title-arrow.open {
  background: #fff url(images/icon-down.jpg) no-repeat 13px 18px;
  background-size: 35%;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title-section {
  height: 40px;
  width: 100%;
  background: #fff;
  display: none;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title-section .tab-title-section-text {
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  line-height: 45px;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-more-section {
  background: rgba(255, 255, 255, 0.9);
  position: absolute;
  width: 100%;
  z-index: 400;
  display: none;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-more-section .tab-more-item {
  display: inline-block;
  width: 25%;
  text-align: center;
  height: 40px;
  line-height: 40px;
  float: left;
  color: #333;
  position: relative;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-more-section .tab-more-item.cur {
  color: #00c775;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-more-section .tab-more-item.cur:after {
  content: '';
  display: inline-block;
  position: absolute;
  width: 80%;
  left: 50%;
  margin-left: -40%;
  bottom: 0;
  border-bottom: 2px solid #00c775;
  z-index: 2;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title {
  position: relative;
  width: 100%;
  height: 40px;
  background: #fff;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle {
  position: absolute;
  height: 40px;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle ul {
  padding: 0;
  margin: 0;
  height: 40px;
  width: 1150px;
  overflow: hidden;
  background-color: #fff;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle ul.lessul {
  display: -webkit-flex;
  display: flex;
  width: 100%;
  -webkit-flex-direction: row;
  flex-direction: row;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle ul.lessul .lesstab-title-item {
  -webkit-flex: 1;
  flex: 1;
  text-align: center;
  line-height: 40px;
  font-size: 16px;
  color: #333;
  position: relative;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle ul.lessul .lesstab-title-item.cur {
  color: #00c775;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle ul.lessul .lesstab-title-item.cur:after {
  content: '';
  display: inline-block;
  position: absolute;
  width: 80%;
  left: 50%;
  margin-left: -40%;
  bottom: 0;
  border-bottom: 2px solid #00c775;
  z-index: 2;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle .tab-title-item {
  display: inline-block;
  height: 40px;
  padding: 0 7px;
  float: left;
  line-height: 40px;
  font-size: 16px;
  color: #333;
  position: relative;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle .tab-title-item.cur {
  color: #00c775;
}
#page > .content > .goldprotemplate .tab-box .tab-section .tab-title .scrolltitle .tab-title-item.cur:after {
  content: '';
  display: inline-block;
  position: absolute;
  width: 80%;
  left: 50%;
  margin-left: -40%;
  bottom: 0;
  border-bottom: 2px solid #00c775;
  z-index: 2;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont {
  margin: 0 0.5%;
  min-height: 600px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item {
  margin: 0.5%;
  width: 49%;
  background: #fff;
  border-radius: 4px;
  float: left;
  position: relative;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo {
  display: block;
  padding: 10px;
  position: relative;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo img {
  height: 140px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box {
  padding: 0 5px;
  position: absolute;
  left: 0;
  bottom: 0;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box span {
  font-size: 12px;
  padding: 2px 2px;
  line-height: 12px;
  color: #fff;
  border-radius: 4px 0 0 4px;
  background: #2b343f;
  display: inline-block;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box i {
  font-size: 12px;
  display: inline-block;
  background: #fff;
  color: #666;
  border: 1px solid #2b343f;
  padding: 0 2px;
  border-radius: 0 4px 4px 0;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box .control-price {
  float: left;
  display: flex;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box .control-price i {
  flex: 1;
  line-height: 14px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box .control-gross {
  float: left;
  display: flex;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box .control-gross i {
  flex: 1;
  line-height: 14px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box .control-gross span {
  margin-left: 3px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .commonName {
  height: 24px;
  display: block;
  line-height: 24px;
  padding-left: 5px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .commonName .dujia {
  float: left;
  color: #fff;
  padding: 1px 2px;
  font-size: 12px;
  line-height: 12px;
  border-radius: 3px;
  font-weight: 400;
  background: #27adff;
  margin-top: 5px;
  margin-right: 3px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .commonName .name {
  color: #333;
  font-size: 14px;
  line-height: 24px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .price {
  color: #ff2400;
  padding: 5px;
  font-weight: bold;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .norms {
  margin: 0 5px 3px;
  font-size: 12px;
  line-height: 14px;
  height: 14px;
  color: #999;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .norms .zhonbao-des {
  margin-left: 5px;
  display: inline;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .biaoqian {
  height: 20px;
  display: flex;
  flex-direction: rows;
  align-items: center;
  padding-left: 5px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .biaoqian div {
  padding: 1px 3px;
  margin-right: 5px;
  line-height: 12px;
  font-size: 12px;
  color: #fff;
  border-radius: 3px;
  display: inline-block;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .biaoqian div.quan {
  background: #ff0000;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .biaoqian div.linqi {
  background: #ff9400;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .biaoqian div.normal {
  color: #ff0e0e;
  padding: 0px 2px;
  border: 1px solid #ff9595;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .biaoqian div.fubiao {
  background: #e4e4e4;
  color: #333;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .handle {
  width: 94%;
  margin: 0px auto 8px;
  height: 27px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .handle .inp-total {
  display: inline-block;
  width: 35px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  opacity: 1;
  color: #333;
  border: none;
  font-size: 12px;
  background: #efefef;
  float: right;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .handle .min {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 15px 0 0 15px;
  color: #666;
  float: right;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .handle .add {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 0 15px 15px 0;
  color: #666;
  float: right;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .handle-lon {
  width: 94%;
  margin: 0 auto 8px;
  height: 27px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .handle-lon .add-lon {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 50%;
  color: #666;
  float: right;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .gone-icon {
  display: block;
  position: absolute;
  width: 100%;
  height: 65%;
  background: url(images/<EMAIL>) center center no-repeat;
  background-size: 30%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .markerUrl {
  position: absolute;
  width: 40px;
  height: 48px;
  top: -2px;
  left: -1px;
}
#page > .content > .goldprotemplate .chu-box-da .goldprotemplate-btm .goldprotemplate-btn {
  display: none;
  margin: 30px auto;
  font-size: 12px;
  width: 180px;
  text-align: center;
  line-height: normal;
  padding: 8px 0;
  border-radius: 13px;
}
@media (max-width: 480px) {
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo img {
    height: 166px;
  }
}
@media (max-width: 414px) {
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo img {
    height: 159px;
  }
}
@media (max-width: 412px) {
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo img {
    height: 158px;
  }
}
@media (max-width: 375px) {
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo img {
    height: 162px;
  }
}
@media (max-width: 350px) {
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo img {
    height: 155px;
  }
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box {
    height: 40px;
  }
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box span {
    width: 54px;
    text-align: center;
  }
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box i {
    text-align: center;
    padding: 0 8px;
  }
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box .control-gross span {
    margin-left: 0;
  }
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo .control-box .control-gross {
    margin-top: 3px;
  }
}
@media (max-width: 320px) {
  #page > .content > .goldprotemplate .chu-box-da .goldprotemplate-list .goldprotemplate-cont .goldprotemplate-item .photo img {
    height: 135px;
  }
  #page > .content > .goldprotemplate .tab-more-item {
    font-size: 12px!important;
  }
}
