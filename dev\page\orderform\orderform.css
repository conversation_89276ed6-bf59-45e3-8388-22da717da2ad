#page > .content > .orderform {
  overflow-y: auto;
  background: #fff;
  border-top: none;
}
#page > .content > .orderform .orderform-list .orderform-item {
  border-bottom: 10px solid #FAFAFA;
  padding-top: 10px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box {
  padding: 0 10px;
  display: flex;
  display: -webkit-flex;
  flex-direction: row;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .img-box {
  float: left;
  width: 79px;
  height: 79px;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #E4E4E4;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg {
  float: left;
  margin-left: 10px;
  flex: 1;
  -webkit-flex: 1;
  padding-right: 5px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg .med-name {
  font-size: 16px;
  line-height: normal;
  font-weight: 500;
  color: #000;
  margin: 6px 0 1px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg .med-spec {
  font-size: 14px;
  line-height: 18px;
  color: #999;
  white-space: nowrap;
  text-overflow: ellipsis;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg .biaoqian-box {
  margin-top: 2px;
  height: 18px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg .biaoqian-box .biaoqian {
  font-size: 12px;
  line-height: 14px;
  padding: 1px 4px;
  display: inline-block;
  color: #fff;
  border-radius: 4px;
  float: left;
  margin-right: 3px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg .biaoqian-box .biaoqian.quan {
  background: #ff0000;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg .biaoqian-box .biaoqian.linqi {
  background: #ff9400;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg .biaoqian-box .biaoqian.normal {
  color: #ff0e0e;
  padding: 0 2px;
  border: 1px solid #ff9595;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-mesg .biaoqian-box .biaoqian.fubiao {
  background: #e4e4e4;
  color: #333;
}
#page > .content > .orderform .orderform-list .orderform-item .item-mesg-box .med-num {
  float: right;
  font-size: 14px;
  color: #999;
  margin-top: 20px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-price-box {
  display: flex;
  display: -webkit-flex;
  flex-direction: row;
  -webkit-flex-direction: row;
  justify-content: space-around;
  -webkit-justify-content: space-around;
  background: #FAFAFA;
  margin: 7px 6px 8px 9px;
  font-size: 14px;
  color: #666;
  padding: 4px 0 8px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-price-box .orgin-price-box {
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  -webkit-flex-direction: column;
}
#page > .content > .orderform .orderform-list .orderform-item .item-price-box .orgin-price-box .title-box {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  -webkit-align-items: center;
  -webkit-justify-content: space-between;
  width: 70px;
  height: 26px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-price-box .orgin-price-box .price {
  line-height: 24px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-price-box .order-price .title {
  line-height: 26px;
}
#page > .content > .orderform .orderform-list .orderform-item .item-price-box .order-price .price {
  color: #FF0000;
  font-weight: 500;
  line-height: 24px;
}
#page > .content > .orderform .nodata {
  display: none;
}
#page > .content > .orderform .nodata .imgbox {
  width: 50%;
  margin: 20% 25% 0 25%;
}
#page > .content > .orderform .nodata .nodata-text {
  font-size: 14px;
  color: #999999;
  text-align: center;
  width: 70%;
  line-height: 18px;
  margin: 5px auto 0;
}
#page > .content > .orderform .nodata .sign {
  display: block;
  width: 88px;
  padding: 4px 0 4px 2px;
  font-size: 14px;
  text-align: center;
  color: #fff;
  background: #3E8EEB;
  margin: 22px auto 0;
  border-radius: 16px;
}
