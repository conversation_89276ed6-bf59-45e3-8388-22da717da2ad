'use strict';
define(['module', 'common/kernel/kernel', 'common/zepto/zepto'], function(module, kernel, zepto) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);

	var $dom = $("#page>.content>.message");
	var scrollStar=true;
	var limitPage=10;
	var curPage=1;
	var totalPage;
	
		$dom.scroll(function(){
			var bheight = $(document).height();//获取窗口高度
			var sheight = kernel.getScrollHeight(dom);//获取滚动条高度，[0]是为了把jq对象转化为js对象
			var stop = $dom.scrollTop();//滚动条距离顶部的距离
			if(stop>=sheight-bheight && stop>1){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时
				if (scrollStar) {
					scrollStar=false;
					setTimeout(function(){
						if(curPage>=totalPage){
							if(totalPage!=0){
							$(".message-list").append("<p style='text-align:center;padding:25px 0;'>无更多数据</p>");
							}
						}else{
							getDataList(curPage);
							curPage++;
						}
					},100);	
				}
				
			}
		});
		
	function getDataList(cur){
		$.ajax({
			type: "GET",
			url: apiUrl+"/app/messageCenter/findMessageInfo",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,limit:limitPage,offset:cur},
			dataType: "json",
			success: function(data) {
				var msgTemp=" ";
				var msgTarget=$(".message-list");
				totalPage = data.messageInfo.pageCount;
				
				for(var i=0;i<data.messageInfo.rows.length;i++){
					msgTemp+='<div class="message-item">\
								<div class="message-time">'+data.messageInfo.rows[i].sendTime+'</div>\
								<div class="message-cont">\
									<div class="message-top">';
					if(data.messageInfo.rows[i].state==0){
						msgTemp+='<span class="tips"></span>';
					}
					msgTemp+='<h4 class="message-title">'+data.messageInfo.rows[i].title+'</h4></div>\
									<div class="message-text">'+data.messageInfo.rows[i].content+'</div>\
									<div class="message-bot">\
										<span class="state">全文</span>\
										<span class="icon-down"></span>\
									</div>\
								</div>\
							</div>';
				}
				if(data.messageInfo.rows.length==0){
					msgTarget.append("<div style='text-align:center;margin-top:100px;'><img src='/static/public/events/0512/no-messages.jpg'></div>")
				}else{
					msgTarget.append(msgTemp);
					scrollStar=true;
				}
				
				$(".message-bot").unbind();
				$(".message-bot").bind('tap',function(){
					if ($(this).prev(".message-text").css("height")=="32px") {
						$(this).prev(".message-text").animate({
							height:"150px"
						}, 500, 'ease-in-out');
						$(this).children(".state").text("收起");
						$(this).children(".icon-down").css({
							transform:'rotate(180deg)',
							transition: 'all 0.3s ease',
							top: '0'
						});
					}else if($(this).prev(".message-text").css("height")=="150px"){
						$(this).prev(".message-text").animate({
							height:"32px"
						}, 500, 'ease-in-out');
						$(this).children(".state").text("全文");
						$(this).children(".icon-down").css({
							transform:'rotate(360deg)',
							transition: 'all 0.3s ease',
							top: '5px'
						});
					}
					
				});
			}
		});

	};
	getDataList(0);
	return {
		onload: function(force) {
			
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});