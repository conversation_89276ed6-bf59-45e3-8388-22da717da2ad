#page>.content>.autumn{
    background: #fdf1e5;
    border-top: none;
    overflow-y: auto;
    .autumn-brands{
        .autumn-abox{
            display:inline-block;
            width:25%;
            float: left;
        }
    }
    .autumn-tab-box{
        position: relative;
        background-color: #fdf1e5;
    }
    .autumn-tab{
        background-color: #fdf1e5;
        width: 100%;
        &.fixed{
            position: fixed;
            top: -1px;
            z-index: 999;
            width: 100%;
        }
    }
    .autumn-tab-item{
        display: inline-block;
        width: 20%;
        float: left;
        background:#fdf1e5;
        .autumn-tab-org{
            display:block;
            &.cur1{
                display:none;
            }
        }
        .autumn-tab-bt{
            display:none;
            &.cur2{
                display: block;
            }
        }
    }
    .autumn-top-item{
        display: inline-block;
        width: 25%;
        float: left;
        &.cur{
            border-bottom: 2px solid #f16500;
           
        }
    }
    .autumn-title{
        display: block;
        font-size:14px;
        padding-left:17px;
        background:url(/static/public/events/1023/bg11.jpg) no-repeat 12px center/2px 12px;
        h4{
            margin:0;
            font-weight:400;
            line-height:32px;
        }
    }
    .autumn-load{
        text-align:center;
        a{
            color:#666;
            font-size:13px;
            line-height:31px;
        }
    }
    .autumn-list{
        
            .autumn-item{
                margin: 0.5%;
                width: 49%;
                background: #fff;
                border-radius: 4px;
                float: left;
                position: relative;
                .photo{
                    display: block;
                    padding: 10px;
                    position:relative;
                    img{
                        height: 140px;
                    }
                    .control-box{
                        padding:0 5px;
                        position:absolute;
                        left:0;
                        bottom:0;
                        span{
                            font-size:12px;
                            padding:2px 2px;
                            line-height:12px;
                            color:#fff;
                            border-radius:4px 0 0 4px;
                            background:#2b343f;
                            display:inline-block;
                        }
                        i{
                            font-size:12px;
                            display:inline-block;
                            background:#fff;
                            color:#666;
                            border:1px solid #2b343f;
                            padding:0 2px;
                            border-radius: 0 4px 4px 0;
                        }
                        .control-price{
                            float:left;
                            display: flex;
                            i{
                                flex:1;
                                line-height:14px;
                            }
                        }
                        .control-gross{
                            float:left; 
                            display: flex;
                            i{
                                flex:1;
                                line-height:14px;
                            }  
                            span{
                                margin-left:3px;
                            }
                        }
                    }
                }
                .commonName{
                    height:24px;
                    display:block;
                    line-height:24px;
                    padding-left:5px;
                    .dujia{
                        float:left;
                        color:#fff;
                        padding:1px 2px;
                        font-size:12px;
                        line-height:12px;
                        border-radius:3px;
                        font-weight:400;
                        background:#27adff;
                        margin-top:5px;
                        margin-right:3px;
                    }
                    .name{
                        color: #333;
                        font-size:14px;
                        line-height:24px;
                    }
                }
                .price{
                    color: #ff2400;
                    padding: 5px;
                    font-weight: bold;
                }
                .norms{
                    margin:0 5px 3px;
                    font-size: 12px;
                    line-height:14px;
                    height:14px;
                    color: #999;
                    .zhonbao-des{
                        margin-left:5px;
                        display:inline;
                    }
                }
                .biaoqian{
                    height:20px;
                    display:flex;
                    flex-direction:rows;
                    align-items:center;
                    padding-left: 5px;
                    div{
                        padding:1px 3px;
                        margin-right:5px;
                        line-height:12px;
                        font-size:12px;
                        color:#fff;
                        border-radius:3px;
                        display:inline-block;
                        &.quan{
                            background:#ff0000;
                        }
                        &.linqi{
                            background:#ff9400;
                        }
                        &.normal{
                            color:#ff0e0e;
                            padding:0px 2px;
                            border:1px solid #ff9595;
                        }
                        &.fubiao{
                            background:#e4e4e4;
                            color:#333;
                        }
                    }
                }
                .handle{
                    width: 94%;
                    margin: 0px auto 8px;
                    height: 27px;
                    .inp-total{
                        display: inline-block;
                        width: 35px;
                        height: 25px;
                        text-align: center;
                        line-height: 25px;
                        opacity: 1;
                        color: #333;
                        border: none;
                        font-size: 12px;
                        background: #efefef;
                        float: right;
                        border-top: 1px solid #ccc;
                        border-bottom: 1px solid #ccc;
                    }
                    .min{
                        display: inline-block;
                        width: 25px;
                        height: 25px;
                        text-align: center;
                        line-height: 22px;
                        font-size: 24px;
                        border: 1px solid #ccc;
                        border-radius: 15px 0 0 15px;
                        color: #666;
                        float: right;
                    }
                    .add{
                        display: inline-block;
                        width: 25px;
                        height: 25px;
                        text-align: center;
                        line-height: 22px;
                        font-size: 24px;
                        border: 1px solid #ccc;
                        border-radius: 0 15px 15px 0;
                        color: #666;
                        float: right;
                    }
                    
                }
                .handle-lon{
                    width: 94%;
                    margin: 0 auto 8px;
                    height: 27px;
                    .add-lon{
                        display: inline-block;
                        width: 25px;
                        height: 25px;
                        text-align: center;
                        line-height: 22px;
                        font-size: 24px;
                        border: 1px solid #ccc;
                        border-radius: 50%;
                        color: #666;
                        float: right;
                    }
                }
                .gone-icon{
                    display: block;
                    position: absolute;
                    width: 100%;
                    height: 65%;
                    background: url(./images/<EMAIL>) center center no-repeat;
                    background-size: 30%;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                }
                .markerUrl{
                    position: absolute;
                    width: 40px;
                    height: 48px;
                    top: -2px;
                    left: -1px;
                }

            }

    }
}


@media(max-width:375px) {
    #page > .content > .autumn .autumn-list .autumn-cont .autumn-item .photo img {
        height: 162px;
    }
    #page > .content > .autumn .autumn-list .autumn-cont{
        min-height: 1419px;
    }
    #page > .content > .autumn .autumn-gross{
        min-height: 523px;
    }
}
@media(max-width:350px) {
    #page > .content > .autumn .autumn-list .autumn-cont .autumn-item .photo img {
        height: 155px;
    }
    #page > .content > .autumn .autumn-list .autumn-cont{
        min-height: 1383px;
    }
    #page > .content > .autumn .autumn-list .autumn-gross .autumn-item .photo .control-box,#page > .content > .autumn .autumn-list .autumn-cont .autumn-item .photo .control-box{
        height:40px;
        span{
            width:54px;
            text-align:center;
        }
        i{
            text-align:center;
            padding:0 8px;
        }
        .control-gross span{
            margin-left:0;
        }
        .control-gross{
            margin-top:3px;
        }
    }
}
@media(max-width:321px) {
    #page > .content > .autumn .autumn-list .autumn-cont .autumn-item .photo img {
        height: 135px;
    }
    #page > .content > .autumn .autumn-list .autumn-cont{
        min-height: 1281px;
    }
}