'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/IScroll/IScroll'], function(module, kernel, jquery, IScroll) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.goldprotemplate");	
	// 获取url拼接的参数
	var tabIndex = kernel.location.args.tabIndex;//页面跳转的tab
	tabIndex = tabIndex ? tabIndex : 0;
	var activityId = kernel.location.args.activityid;//活动的id;
	var tabDataArr;//页面tab的数据
	var chooseCode;//被选择的exhibitionId
    var scrollTop1;//tab距离屏幕上方的高度
    var activeteditem;
    var myscroll;
    var scrollStar = true;
    var pagecur = 0;
    var btnbgcolor;
    var btntextcolor;
    var totalPage;
    var btnshow = true;
   	//获取页面的初始数据
	$.ajax({
		type:'get',
		url:apiUrl+"/app/activity/findActivity?activityId="+activityId,
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success:function(res){
			$(".goldprotemplate").css("background",res.data.bgRes);
			$(".goldprotemplate-banner img").attr("src",hostUrl+res.data.appBgImage);
			/*var img =new Image();  
		    img.onload =function(){  
		        img.onload =null;  
		        document.querySelector(".goldprotemplate-banner").append(img) ;
		        scrollTop1 = $('.goldprotemplate .tab-box').offset().top;
		    }  
		    img.src = hostUrl+res.data.appBgImage;*/  
			btnbgcolor = res.data.buttonBgColor;
			btntextcolor = res.data.buttonTextColor;
			tabDataArr = res.data.groups;
			var styleEle = document.createElement('style');
			styleEle.type = 'text/css';
			styleEle.innerHTML='.activeteditem{color:'+res.data.tabSelectedTextColor+'!important}.activeteditem::after{content:"";display: inline-block;position: absolute;width: 80%;left: 50%;margin-left: -40%;bottom: 0;border-bottom: 2px solid '+res.data.tabSelectedTextColor+';z-index: 2;}.submit-btn{background:'+res.data.buttonBgColor+';color:'+res.data.buttonTextColor+'}';
			$('head').append(styleEle);
			var tabstemp ='';
			if(tabDataArr.length <=4){
				tabstemp += '<div class="tab-title"><div class="scrolltitle" style="width:100%"><ul class="lessul">'
				for(var i in tabDataArr){
					tabstemp += '<li class="lesstab-title-item tab-item">'+tabDataArr[i].title+'</li>'
				}
				tabstemp +='</ul></div></div>';
				$(".tab-section").append(tabstemp);
			}else{
				tabstemp += '<div class="tab-title"><div class="scrolltitle"><ul>';
				for(var i in tabDataArr){
					tabstemp += '<li class="tab-title-item tab-item">'+tabDataArr[i].title+'</li>'
				}
				tabstemp += '</ul></div></div><div class="tab-title-section"><span class="tab-title-section-text">全部分类</span></div><div class="tab-title-arrow"></div><div class="tab-more-section">'
				for(var i in tabDataArr){
					tabstemp += '<div class="tab-more-item">'+tabDataArr[i].title+'</div>'
				}
				tabstemp += '</div>';
				$(".tab-section").append(tabstemp);
					$(".tab-title-arrow").click(function(){
					$(".tab-title").toggle();
					$(".tab-title-section").toggle();
					$(".tab-more-section").toggle();
					$(this).toggleClass("open");
					
				});
				$($(".tab-section .tab-more-item")[tabIndex]).addClass("activeteditem");
				myscroll=new IScroll(".tab-title",{scrollX: true, scrollbars: false, click: true });
			}
			chooseCode = tabDataArr[tabIndex].exhibitionId;//初始exhibitionId
			if(tabIndex){
				changeClass();
				if(tabDataArr.length > 4){
					tabscroll();
				}
			}else{
				$($('.tab-section ul li')[tabIndex]).addClass("activeteditem");
			}
			getDataList(pagecur);//请求数据
			$(".tab-more-item").click(function(){
				scrollTop1 = document.querySelector(".tab-box").offsetTop;
				btnshow = true;
				pagecur = 0;
				$(".goldprotemplate-btn").hide();
				$(".tab-title").show();
				$(".tab-title-section").hide();
				$(".tab-more-section").hide();
				$(".tab-title-arrow").removeClass("open");
				tabIndex = $(this).index();
				chooseCode = tabDataArr[tabIndex].exhibitionId;
				$(".goldprotemplate-cont").empty();
				getDataList(pagecur);
				kernel.showLoading();
				if(tabDataArr.length > 4){
					tabscroll();
				}
				changeClass();
			});

			$('.tab-item').click(function(){
				scrollTop1 = document.querySelector(".tab-box").offsetTop;
				btnshow = true;
				tabIndex = $(this).index();
				chooseCode = tabDataArr[tabIndex].exhibitionId;
				pagecur = 0;
				$(".goldprotemplate-btn").hide();
				$(".goldprotemplate-cont").empty();
				getDataList(pagecur);
				kernel.showLoading();
				if(tabDataArr.length > 4){
					tabscroll();
				}
				changeClass();

				$dom.animate({  
		            scrollTop: scrollTop1
		        }, 0);
		        $('.goldprotemplate .tab-section').css("position","fixed");
		        $(".baktitlebox").show();
			});
		}
	})

   $dom.scroll(function(){
   		if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
		scrollTop1 = document.querySelector(".tab-box").offsetTop;
		var sHtop = $(dom).scrollTop();
		if(sHtop >= scrollTop1){
			$('.goldprotemplate .tab-section').css("position","fixed");
		}else{
			$('.goldprotemplate .tab-section').css("position","relative");
			$('.goldprotemplate .tab-title').css("display",'block');
		}
		$('.goldprotemplate .tab-more-section').css('display',"none");
		$('.goldprotemplate .tab-title').css('display',"block");
		$('.goldprotemplate .tab-title-section').css('display',"none");
		$('.goldprotemplate .tab-title-arrow').removeClass("open");

		var bheight = $(document).height();//获取窗口高度
		var sheight = kernel.getScrollHeight(dom);//获取滚动条高度，[0]是为了把jq对象转化为js对象
		if(sHtop>=sheight-bheight && sHtop>10 && scrollStar){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时
			if(pagecur>=(totalPage -1) || pagecur >= Math.floor(tabDataArr[tabIndex].maxLimit / 10)){
				if(btnshow){
					btnshow = false;
					$(".goldprotemplate-btn").show();
					$(".goldprotemplate-btm .goldprotemplate-btn").css({
						background:btnbgcolor,
						color:btntextcolor
					})
					changebtnHtml();
					$('.goldprotemplate .goldprotemplate-btn').unbind();
					$('.goldprotemplate .goldprotemplate-btn').click(function(){
						$(".goldprotemplate-cont").empty();
						kernel.showLoading();
						$(".goldprotemplate-btn").hide();
						pagecur = 0;
						btnshow = true;
						getDataList(pagecur);
						if(tabDataArr.length > 4){
							tabscroll();
						}
						changeClass();
						$dom.animate({  
				            scrollTop: scrollTop1
				        }, 0);
				        $('.goldprotemplate .tab-section').css("position","fixed");
					})
				}
			}else{
				scrollStar=false;
				pagecur++;
				getDataList(pagecur);	
			}	
		}
	});





	function getDataList(pagecur){
		$('.load-tip').css('display','block')
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId="+chooseCode+"&sort=1&limit=10",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,offset:pagecur},
			dataType: "json",
			success: function(res) {
				if(res.status == "success"){
					var goldprotemplateTemp = " ";
					var goldprotemplateTarget = $(".goldprotemplate-cont");
					totalPage = res.data.pageCount;
					$('.load-tip').css('display','none');
					if(res.data.rows){
						for(var i=0;i<res.data.rows.length;i++){
							goldprotemplateTemp+='<div class="goldprotemplate-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
							if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
								goldprotemplateTemp += '<div class="control-box">';
									if(res.data.rows[i].uniformPrice){
										goldprotemplateTemp += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
									};
									if(res.data.rows[i].suggestPrice){
										goldprotemplateTemp += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
									};
									if(res.data.rows[i].grossMargin){
										goldprotemplateTemp += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
									};
								goldprotemplateTemp+='</div></a>'
							}
							goldprotemplateTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
								if(res.data.rows[i].agent == 1){
									goldprotemplateTemp+='<div class="dujia">独家</div>'
								}
							goldprotemplateTemp+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
							goldprotemplateTemp+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
							goldprotemplateTemp+='<div class="biaoqian">';
							if(res.data.rows[i].tagList){
								for(var j in res.data.rows[i].tagList.slice(0,3)){
									switch(res.data.rows[i].tagList[j].uiType){
										case 1:goldprotemplateTemp+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 2:goldprotemplateTemp+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 3:goldprotemplateTemp+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 4:goldprotemplateTemp+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
										default:;break;
									}
								}
							}
							goldprotemplateTemp+='</div>'
								if(res.data.rows[i].isControl==1){
										if(res.data.rows[i].isPurchase==true){
											if(res.data.rows[i].isControlPriceToMe==1){
												goldprotemplateTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
											}else{
												if(res.data.rows[i].priceType==1){
													goldprotemplateTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
												}else{
													goldprotemplateTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
												}
											}
											
												// if (res.data.rows[i].isSplit==0) {
													// goldprotemplateTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
												/*}else{
													goldprotemplateTemp+='<div class="zhonbao-des"></div>';
												}*/
													goldprotemplateTemp+='<div class="handle">\
															<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
															<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
															<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
														</div>';
												
										}else{
											goldprotemplateTemp+='<div class="price">暂无购买权限</div>';
											goldprotemplateTemp += '<div class="control-box"></div>';
											// goldprotemplateTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
											goldprotemplateTemp+='<div class="handle-lon"></div>';
										}
								}else{
									if(res.data.rows[i].isControlPriceToMe==1){
										goldprotemplateTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										if(res.data.rows[i].priceType==1){
											goldprotemplateTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											goldprotemplateTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
										}
									}
						
									// if (res.data.rows[i].isSplit==0) {
										// goldprotemplateTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										goldprotemplateTemp+='<div class="zhonbao-des"></div>';
									}*/
										goldprotemplateTemp+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
								}
								
							
								if(res.data.rows[i].status=="2"){
									goldprotemplateTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
								};
								if(res.data.rows[i].markerUrl){
									goldprotemplateTemp+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
								}
								
							goldprotemplateTemp+='</div>';
						}
					}
					goldprotemplateTarget.append(goldprotemplateTemp);
					
					rowAddMin();

					kernel.hideLoading();

					scrollStar=true;
				}
			}
		});
	}
	
	function tabscroll(){
		var toScrollX = -55 * tabIndex;
		myscroll.scrollTo(toScrollX, 0, 400);
	}

	function changebtnHtml(){
		if(tabIndex < tabDataArr.length-1){
			$('.goldprotemplate-btn').html('点击跳转至“'+tabDataArr[tabIndex+1].title+'”');
			chooseCode=tabDataArr[tabIndex+1].exhibitionId;
			tabIndex +=1;
		}else{
			$('.goldprotemplate-btn').html('点击跳转至“'+tabDataArr[0].title+'”');
			chooseCode=tabDataArr[0].exhibitionId;
			tabIndex = 0 ;
		}
		
	}

	function changeClass(){
		$('.tab-item').eq(tabIndex).addClass('activeteditem').siblings().removeClass('activeteditem');
		$('.tab-more-item').eq(tabIndex).addClass('activeteditem').siblings().removeClass('activeteditem');
	}
	
	
	return {
		onload: function(force) {

		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});