#page>.content>.discountcoupon{
	overflow-y:auto; 
	border-top: none;
	background: #7641df;
	color: #f19424;
	.hb-box2{
		width: 100%;
		background: url('/static/public/events/0329/quan3.jpg') center top repeat-y; 
		background-size: 100%;
	}
	.redbao-item{
		width: 75%;
		margin: 0 auto;
		margin-bottom: 6px;
		.redbao-title{
			height: 15px;
			width: 43%;
			text-align: center;
			background: -moz-radial-gradient(#fff, #fff, #fbe1c2);
 			background: -webkit-radial-gradient(#fff, #fff, #fbe1c2);
 			border-radius: 5px 5px 0 0;
 			font-size: 12px;
		}
		.redbao-cont{
			height: 70px;
			.redbao-cont-l{
				width: 75%;
				float: left;
				height:70px;
				.redbao-amount{
					display: inline-block;
					width: 30%;
					float: left;
					margin: 15px 0 0 10px;
					.redbao-amount-unt{
						font-size: 16px;
					}
					.redbao-amount-num{
						font-size: 26px;
						font-weight: 400;
					}
				}
				.redbao-plain{
					display: inline-block;
					width: 63%;
					float: left;
					margin-left: 4px;
					.redbao-plain-top{
						display: inline-block;
						font-weight: bold;
						padding: 0 1px;
						background: #f1a124;
						color: #fff;
						margin: 15px 0 0 5px;
					}
					.redbao-plain-cen{
						width: 90%;
						margin-top: 3px;
						margin-bottom: 5px;
					}
					.redbao-plain-bot{
						font-size: 12px;
						padding-left: 5px;
						
					}
				}
				
			}
			.redbao-cont-r{
				width: 25%;
				float: right;
				height:70px;
				.redbao-type1{
					font-size: 12px;
					color: #fff;
					.redbao-type-text{
						margin: 14px 0 0 6px;
					}
					.cutday{
						color: #2b2b2b;
					}
					.cutime{
						height: 20px;
					}
					.abo{
						background: #fff;
						color: #333;
						display: inline-block;
						width: 14px;
						height: 14px;
						line-height: 15px;
						text-align: center;
						position: relative;
						top: 2px;
						border-radius: 3px;
						margin: 0 1px;
					}
				}
				.redbao-type2{
					.cro-pross{
						margin:12px auto;
						width:70%;
						position: relative;
						padding-bottom: 10px;
						.cro-pross-text{
							position: absolute;
							width: 100%;
							text-align: center;
							font-size: 10px;
							color: #fdff61;
							padding-top: 5px;
						}
					}
				}
				
			}
		}
	}
	.redbao-cont-l{
		background: url('./images/quanbg1.png') center top no-repeat; 
		background-size: 100%;
	}
	.redbao-item1 .redbao-cont-r{
		background: url('./images/quanbg5.png') center top no-repeat; 
		background-size: 100%;
	}
	.redbao-item2 .redbao-cont-r{
		background: url('./images/quanbg3.png') center top no-repeat; 
		background-size: 100%;
	}
	.redbao-item3 .redbao-cont-r{
		background: url('./images/quanbg4.png') center top no-repeat; 
		background-size: 100%;
	}
}
@media(max-width:320px) {
	#page > .content > .discountcoupon .redbao-item .redbao-cont .redbao-cont-l .redbao-amount .redbao-amount-num{
		font-size: 24px;
	}
	#page > .content > .discountcoupon .redbao-item .redbao-cont .redbao-cont-l .redbao-plain .redbao-plain-cen{
		margin: 0 0 3px 0;
	}
	#page > .content > .discountcoupon .redbao-item .redbao-cont .redbao-cont-r .redbao-type1 .redbao-type-text{
		margin: 8px 0 0 6px;
	}
	#page > .content > .discountcoupon .redbao-item .redbao-cont .redbao-cont-r .redbao-type1 .abo{
		margin: 0;
	}
	#page > .content > .discountcoupon .redbao-item .redbao-cont .redbao-cont-l .redbao-plain{
		margin-left: 0;
		width: 68%;
	}
	#page > .content > .discountcoupon .redbao-item .redbao-cont .redbao-cont-l .redbao-plain .redbao-plain-top{
		margin: 13px 0 0 5px;
	}
	#page > .content > .discountcoupon .redbao-item .redbao-cont .redbao-cont-l .redbao-plain .redbao-plain-bot{
		padding-left: 0;
	}
	#page > .content > .discountcoupon .redbao-item .redbao-cont .redbao-cont-l .redbao-amount{
		width: 26%;
	}
}
