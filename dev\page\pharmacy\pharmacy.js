'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/IScroll/IScroll'], function(module, kernel, jquery, IScroll) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.pharmacy");
	
	// var myscroll=new IScroll(".tab-title",{scrollX: true, scrollbars: false, click: true });

	var pageCur = 0;
	var totalPage;

    $dom.scroll(function(){
    		
    });
    var initDataList=[];
    $.ajax({
			type: "POST",
			url: apiUrl+"/app/yaoDetail/queryYaoCategoryByParentId",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,parentId:0},
			dataType: "json",
			success: function(res) {
				initDataList = JSON.parse(res.yaoCategoryList);
				drawingLevelOneTabs(0)				
		}		
	});

    var titleDataId = 1;
	$(".tabUl").click(function(e){
		var tabindex = $(e.target).index();
		$(e.target).addClass("cur").siblings().removeClass("cur");
		titleDataId = parseInt($(e.target).attr("data-id"));
		pageCur = 0;
		$(".total-video").show();
		$(".paddingbox").css("height","80px");
		$(".total-video .title").html("全部视频")
		if(titleDataId ==3){
			medicineGuide()
			getVideoList(pageCur,3);
		}else if(titleDataId ==4){
			pageCur = 0;
			getVideoList(pageCur,4);
			$(".total-video").hide();
			$(".paddingbox").css("height","40px")
		}else{
			drawingLevelOneTabs(tabindex);
			getVideoList(pageCur,titleDataId);
		}
	})
    /***
      **渲染tab栏的方法
    ***/
	function drawingLevelOneTabs(n){
		var levelOnetabsTemp='';
		$('.total-video').empty();
		var tabsTemp = '<div class="filter"><i>选择分类</i><span></span></div><div class="title">全部视频</div><div class="menubox"></div>';
		$('.total-video').append(tabsTemp);
		$('.total-video .filter').unbind()
		$('.total-video .filter').bind('click',function(){
			$(".total-video .menubox").toggle();
			$(this).find("span").toggleClass('rotatespan')
			var ulHeight1 = $(".levelonemenu").height();
			var ulHeight2 = $(".levletwomenu").height();
			if(ulHeight1 > ulHeight2){
				ulHeight1 >= 400 ? $(".levletwomenu").css("height",400+'px') : $(".levletwomenu").css("height",ulHeight1+40+'px');
				$(".levelonemenu").css("height",ulHeight1+40+'px')
			}else{
				$(".levelonemenu").css("height",ulHeight2+40+'px')
				$(".levletwomenu").css("height",ulHeight2+40+'px')
			};
		})
		levelOnetabsTemp+='<ul class="levelonemenu">'
		for(var i in initDataList[n].childrenList){
			if(i == 0){
				levelOnetabsTemp+='<li class="activeted" parent-idex="'+n+'" level-one="0">'+initDataList[n].childrenList[i].name+'</li>';
			}else{
				levelOnetabsTemp+='<li parent-idex="'+n+'" level-one="'+i+'">'+initDataList[n].childrenList[i].name+'</li>';
			}					
		}
		levelOnetabsTemp += '</ul><ul class="levletwomenu"></ul>';
		$('.total-video .menubox').append(levelOnetabsTemp)
		drawingLevelTwoTabs(n,0)					
		$(".levelonemenu").unbind();
		$(".levelonemenu").click(function(e){
			var parentIdex = parseInt($(e.target).attr("parent-idex"));
			var levelOne = parseInt($(e.target).attr("level-one"));
			$(e.target).addClass('activeted').siblings().removeClass('activeted')
			drawingLevelTwoTabs(parentIdex,levelOne)
		})
	}
	function drawingLevelTwoTabs(m,n){
		var levelTwotabsTemp = '';
		levelTwotabsTemp += '<li class="cur" parent-title="'+initDataList[m].childrenList[n].name+'" level-two="'+initDataList[m].childrenList[n].id+'">全部 <span></span></li>';
		for(var j in initDataList[m].childrenList[n].childrenList){
			levelTwotabsTemp+='<li level-two="'+initDataList[m].childrenList[n].childrenList[j].id+'">'+initDataList[m].childrenList[n].childrenList[j].name+'<span></span></li>';
		}
		levelTwotabsTemp+='';
		$('.total-video .menubox .levletwomenu').empty().append(levelTwotabsTemp);
		$('.total-video .menubox .levletwomenu').unbind();
		$('.total-video .menubox .levletwomenu').click(function(e){
			$('.total-video .filter span').toggleClass('rotatespan')
			$(e.target).addClass('cur').siblings().removeClass('cur');
			$(e.target).children().show();
			$(e.target).siblings().children().hide();
			var levelTwoId = $(e.target).attr('level-two');
			$(e.target).attr('parent-title') ? $(".total-video .title").html($(e.target).attr('parent-title')) : $(".total-video .title").html($(e.target).html())
			pageCur = 0;
			getVideoList(pageCur,levelTwoId);
			$('.nodata').hide();
			$(".total-video .menubox").hide();
		})
	}

	function medicineGuide(){
		var guideTemp ='';
		guideTemp += '<div class="filter"><i>选择分类</i><span></span></div><div class="title">全部视频</div><div class="menubox"><ul class="guideUl"><li class="cur" parent-title="全部视频"  data-id="3">全部视频<span></span></li>'
		for(var i in initDataList[2].childrenList){
			guideTemp += '<li data-id="'+initDataList[2].childrenList[i].id+'">'+initDataList[2].childrenList[i].name+'<span></span></li>'
		}
		guideTemp +='</ul></div>'
		$('.total-video').empty().append(guideTemp);
		$('.total-video .filter').unbind('click')
		$('.total-video .filter').bind('click',function(){
			$(".total-video .menubox").toggle();
			$('.total-video .filter span').toggleClass('rotatespan')
		})
		$('.guideUl').unbind();
		$(".guideUl").click(function(e){
			var dataId=$(e.target).attr('data-id');
			$('.total-video .filter span').toggleClass('rotatespan')
			pageCur = 0;
			getVideoList(pageCur,dataId);
			$(e.target).addClass('cur').siblings().removeClass('cur')
			$(e.target).children().show();
			$(e.target).siblings().children().hide();
			$(".total-video .menubox").toggle();
			$(e.target).attr('parent-title') ? $(".total-video .title").html($(e.target).attr('parent-title')) : $(".total-video .title").html($(e.target).html())
		})
	}

	function addZero(n){
		return n >= 10 ? n : '0'+n;
	}
	
	function timestamp(n){
		var time = new Date(n);
		var year = time.getFullYear();
		var month = time.getMonth() +1;
		var day = time.getDate();
		return year+'-'+addZero(month)+'-'+addZero(day)

	}
	function getVideoList(cur,cateid){
		var videotemp='';
		var videoTarget = $(".pharmacy-list");
		kernel.showLoading();
		$(".nomoredata").hide();
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/yaoDetail/queryList",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,categoryId:cateid,offset:0,limit:1000},
			dataType: "json",
			success: function(res) {
				kernel.hideLoading();
				if(cur == 0){
					$(".pharmacy-list").empty();
					if(res.yaoDetailList.rows.length<=0){
						$('.nodata').show();
						return;
					}else{
						$('.nodata').hide();
					}
				}
				for(var i in res.yaoDetailList.rows){
					videotemp +='<div class="pharmacy-item">\
									<div class="video-mesg">\
										<div class="title">'+res.yaoDetailList.rows[i].title+'</div>\
										<div class="time-watch">\
											<div class="time">'+timestamp(res.yaoDetailList.rows[i].createTime)+'</div>\
											<div class="watchtimes">'+res.yaoDetailList.rows[i].pageviewShow+'次</div>\
										</div>\
									</div>\
									<div class="img-box">\
										<img src="'+hostUrl+res.yaoDetailList.rows[i].viewImage+'" alt="">'
								if(cateid != 4){
									videotemp += '<a href="javascript:;"></a>'
								}
					videotemp +='</div><a href="#!pharmacyvideo&videoId='+res.yaoDetailList.rows[i].id+'" class="linka"></a></div>'
				}
				videoTarget.append(videotemp);
				$(".nomoredata").show();

			}		
		});
	}
	
	getVideoList(0,1);//获取初始页面的数据					
	return {
		onload: function(force) {
			setAppTitle("药学院");
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});