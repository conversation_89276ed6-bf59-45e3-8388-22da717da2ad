#page>.content>.pinpailist{
    overflow-y:auto; 
    background: #fff;
    border-top: none;
    .pinpaibox{
        position: relative;
        z-index: 999;
    }
    .top-tab-cont-popup{
        position: fixed;
        margin: auto;
        right: 0;
        left: 0;
        bottom: 0;
        top: 0;
        background-color: rgba(0,0,0,0.75);
        display: none;
        z-index: 9;
    }
    .pinpai-list-box{
        width:100%;
        overflow-x:auto;
    }
    .pinpai-list-cont{
        height: 185px;
        padding: 5px 0 5px 2px;
        margin-top: -4px;
        width:156%;
    }
    .pin-box{
        min-height:250px;
    }
    .top-tab{
        background:#2f3943;
        padding: 5px 0 5px 8px;
        min-height:50px;
        a{
            display: inline-block;
            float: left;
            width: 14%;
            margin: 4px;
        }
    }
    .top-tab-cont{
        background:#2c3640;
        padding: 5px 0 5px 8px;
        max-height:360px;
        overflow-y: auto;
        a{
            display: inline-block;
            float: left;
            width: 14%;
            margin:4px;
            .tab-title{
                color:#fff;
                font-size:12px;
                overflow: hidden;
                text-align:center;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding-top:5px;
                height:24px;
            }
        }
    }
    .pinpai-link{
        min-height:50px;
        display:block;
    }
   .pinpailist-item {
        background-color: #fff;
        width: 19.2%;
        display: inline-block;
        float: left;
        margin: 8px 2px;
        height:163px;
        position: relative;
        padding: 5px;
        border-radius:5px;
        &.seeall{
            background:#fff url('/static/public/pinpailist/seeall.png') no-repeat left bottom/100%;
        }
         img.photo{
            min-height: 100px;
        } 
        .name {
            color: #333;
            font-size: 13px;
            padding: 2px 5px;
            height: 18px;
            overflow: hidden;
        }
        .price {
            color: #ff2400;
            padding: 3px;
            height: 22px;
        }
        .norms {
            font-size: 12px;
            color: #999;
            padding: 0 10px;
            height: 20px;
        }
        .gone-icon{
            position: absolute;
            width: 100%;
            height: 60%;
            background: url(./images/<EMAIL>) center center no-repeat;
            background-size: 50%;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        .markerUrl{
                    position: absolute;
                    width: 40px;
                    height: 48px;
                    top: -2px;
                    left: -1px;
                }
    }
    
}

 @media (max-width:320px) {
    #page > .content > .pinpailist .top-tab-cont {
        max-height:330px;
    }
    .pinpailist-item{
        width:19.2%!important;
    }
    .pinpai-list-cont{
        width:177%!important;
    }
} 