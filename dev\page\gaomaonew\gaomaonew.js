'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/IScroll/IScroll'], function(module, kernel, jquery, IScroll) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.gaomaonew");	
	var myscroll=new IScroll(".tab-title",{scrollX: true, scrollbars: false, click: true });
	var scrollTop1;
	var itemIndex = 0 ;
	var typeCodeArr=['02hxxt','02kjxy','02xnxg','02wyl','02xhxt','ZS201803291117376765','02zbbj','02gdtj'];
	var chooseCode = typeCodeArr[0]
	var pageCur = 0;
	var flag = true;
	$(".tab-title-arrow").click(function(){
		$(".tab-title").toggle();
		$(".tab-title-section").toggle();
		$(".tab-more-section").toggle();
		$(this).toggleClass("open");
		
	});
	// $('.gaomaonew .tab-section').click(function(){
		
	// 	$dom.animate({  
 //            scrollTop: scrollTop1
 //        }, 0);
 //        $('.gaomaonew .tab-section').css("position","fixed");
	// })

   $dom.scroll(function(){

   		if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
	
		var sHtop = $(dom).scrollTop();
		if(sHtop >= scrollTop1){
			$('.gaomaonew .tab-section').css("position","fixed");
			$(".baktitlebox").show();

		}else{
			$('.gaomaonew .tab-section').css("position","relative");
			$('.gaomaonew .tab-title').css("display",'block');
			$(".baktitlebox").hide();
		}
		$('.gaomaonew .tab-more-section').css('display',"none");
		$('.gaomaonew .tab-title').css('display',"block");
		$('.gaomaonew .tab-title-section').css('display',"none");
		$('.gaomaonew .tab-title-arrow').removeClass("open")
	});


    $.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=02rmtj&sort=1&limit=10",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId},
			dataType: "json",
			success: function(res) {
				var gaomaonewTemp1 = " ";
				var gaomaonewTarget1 = $(".gaomaonew-cont1");
				// if(res.isDig==1){
				// 	$(".wajinbibtn").show();
				// 	$(".wajinbibtn").attr("data-id",res.digId);
				// 	getjinbi(thispage);
				// }else{
				// 	$(".wajinbibtn").hide();
				// }
				for(var i=0;i<res.data.rows.length;i++){
					gaomaonewTemp1+='<div class="gaomaonew-item">\
								<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo">';
								/*if(res.data.rows[i].blackProductText!='' && res.data.rows[i].blackProductText!= null){
									gaomaonewTemp1+='<span class="nofanmark">'+res.data.rows[i].blackProductText+'</span>';
								};*/
								gaomaonewTemp1+='<img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'"></a>\
								<div class="pro-info">\
								<div class="pro-info-title"><a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="pro-title text-overflow">';
								if(res.data.rows[i].agent == 1){
									gaomaonewTemp1+='<div class="dujia">独家</div>'
								}
								gaomaonewTemp1+= res.data.rows[i].commonName+'</a></div><div class="pro-info-nomal text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
								gaomaonewTemp1+='<div class="biaoqian">';
								if(res.data.rows[i].tagList){
									for(var j in res.data.rows[i].tagList.slice(0,3)){
										switch(res.data.rows[i].tagList[j].uiType){
											case 1:gaomaonewTemp1+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
											case 2:gaomaonewTemp1+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
											case 3:gaomaonewTemp1+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
											case 4:gaomaonewTemp1+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
											default:;break;
										}
									}
								}
								gaomaonewTemp1+='</div>'	
									if(res.data.rows[i].isControl==1){
										if(res.data.rows[i].isPurchase==true){
											gaomaonewTemp1 += '<div class="pro-info-profits">';
											if(res.data.rows[i].uniformPrice){
												gaomaonewTemp1 += '<span class="span1">控销价</span class="span2"><span class="span2">￥'+ res.data.rows[i].uniformPrice.toFixed(2)  +'</span>';
											}
											if(res.data.rows[i].suggestPrice){
												gaomaonewTemp1 += '<span class="span1">零售价 </span><span class="span2">￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</span>';
											}
											if(res.data.rows[i].grossMargin){
												gaomaonewTemp1 += '<span class="span3">毛利</span><span class="span4">'+ parseInt(res.data.rows[i].grossMargin) + '%</span>';
											}
											gaomaonewTemp1 += '</div>';
											// if (res.data.rows[i].isSplit==0) {
												// gaomaonewTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
											/*}else{
												gaomaonewTemp1+='<div class="zhonbao-des"></div>';
											}*/
											gaomaonewTemp1+='<div class="pro-info-bot">';
												if(res.data.rows[i].isControlPriceToMe==1){
													gaomaonewTemp1+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
												}else{
													if(res.data.rows[i].priceType==1){
														gaomaonewTemp1+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
													}else{
														gaomaonewTemp1+='<div class="red bot-price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
													}
												}
													gaomaonewTemp1+='<div class="handle">\
														<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
														<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'"  data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
														<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
													</div>';
												
											gaomaonewTemp1+='</div>';
										}else{
											gaomaonewTemp1+='<div class="pro-info-profits"></div><div class="pro-info-bot">\
												<div class="red bot-price"></div>\
												<div class="lackbuy">暂无购买权限</div>\
											</div>';
										}
									}else{
											gaomaonewTemp1 += '<div class="pro-info-profits">';
											if(res.data.rows[i].uniformPrice){
												gaomaonewTemp1 += '<span class="span1">控销价</span class="span2"><span class="span2">￥'+ res.data.rows[i].uniformPrice.toFixed(2)  +'</span>';
											}
											if(res.data.rows[i].suggestPrice){
												gaomaonewTemp1 += '<span class="span1">零售价 </span><span class="span2">￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</span>';
											}
											if(res.data.rows[i].grossMargin){
												gaomaonewTemp1 += '<span class="span3">毛利</span><span class="span4">'+ parseInt(res.data.rows[i].grossMargin) + '%</span>';
											}
											gaomaonewTemp1 += '</div>';
											// if (res.data.rows[i].isSplit==0) {
												// gaomaonewTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
											/*}else{
												gaomaonewTemp1+='<div class="zhonbao-des"></div>';
											}*/
										gaomaonewTemp1+='<div class="pro-info-bot">';
												if(res.data.rows[i].isControlPriceToMe==1){

													gaomaonewTemp1+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
												}else{
													if(res.data.rows[i].priceType==1){
														gaomaonewTemp1+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
													}else{
														gaomaonewTemp1+='<div class="red bot-price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
													}
												}
												gaomaonewTemp1+='<div class="handle">\
														<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
														<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'"  data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
														<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
													</div>';
											
										gaomaonewTemp1+='</div>';
									}
									gaomaonewTemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="a-link-mask"></a>\
								</div>';
								if(res.data.rows[i].status=="2"){
									gaomaonewTemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
								};
								if(res.data.rows[i].markerUrl){
									gaomaonewTemp1+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
								}
							gaomaonewTemp1+='</div>';
				}
				gaomaonewTarget1.empty().append(gaomaonewTemp1);
				scrollTop1 = $(".tab-section").offset().top;
				//console.log(scrollTop1);
				listAddMin();
			}
	});
	

	function getdatalist(){
		$('.load-tip').css('display','block')
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId="+chooseCode+"&sort=1&limit=1000",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,offset:0},
			dataType: "json",
			success: function(res) {
				var gaomaonewTemp2 = " ";
				var gaomaonewTarget2 = $(".gaomaonew-cont2");

				$('.load-tip').css('display','none')
			for(var i=0;i<res.data.rows.length;i++){
				gaomaonewTemp2+='<div class="gaomaonew-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					gaomaonewTemp2 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							gaomaonewTemp2 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							gaomaonewTemp2 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							gaomaonewTemp2 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					gaomaonewTemp2+='</div></a>'
				}
				gaomaonewTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							gaomaonewTemp2+='<div class="dujia">独家</div>'
						}
					gaomaonewTemp2+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				gaomaonewTemp2+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				gaomaonewTemp2+='<div class="biaoqian">';
				if(res.data.rows[i].tagList){
					for(var j in res.data.rows[i].tagList.slice(0,3)){
						switch(res.data.rows[i].tagList[j].uiType){
							case 1:gaomaonewTemp2+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 2:gaomaonewTemp2+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 3:gaomaonewTemp2+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 4:gaomaonewTemp2+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
							default:;break;
						}
					}
				}
				gaomaonewTemp2+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									gaomaonewTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										gaomaonewTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										gaomaonewTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// gaomaonewTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										gaomaonewTemp2+='<div class="zhonbao-des"></div>';
									}*/
										gaomaonewTemp2+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								gaomaonewTemp2+='<div class="price">暂无购买权限</div>';
								gaomaonewTemp2 += '<div class="control-box"></div>';
								// gaomaonewTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								gaomaonewTemp2+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							gaomaonewTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							// console.log(i,res.data.rows[i].priceType)
							if(res.data.rows[i].priceType==1){
								gaomaonewTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								gaomaonewTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// gaomaonewTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							gaomaonewTemp2+='<div class="zhonbao-des"></div>';
						}*/
							gaomaonewTemp2+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						gaomaonewTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						gaomaonewTemp2+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				gaomaonewTemp2+='</div>';
			}
				gaomaonewTarget2.append(gaomaonewTemp2);
				
				rowAddMin();

				kernel.hideLoading();
				switch(itemIndex){
					case 0:$('.gaomaonew-btn').html('点击跳转至“抗菌消炎”');chooseCode="02kjxy";itemIndex = 1;break;
					case 1:$('.gaomaonew-btn').html('点击跳转至“心脑血管”');chooseCode="02xnxg";itemIndex = 2;break;
					case 2:$('.gaomaonew-btn').html('点击跳转至“外用类”');chooseCode="02wyl";itemIndex = 3;break;
					case 3:$('.gaomaonew-btn').html('点击跳转至“消化系统”');chooseCode="02xhxt";itemIndex = 4;break;
					case 4:$('.gaomaonew-btn').html('点击跳转至“妇科类”');chooseCode="ZS201803291117376765";itemIndex = 5;break;
					case 5:$('.gaomaonew-btn').html('点击跳转至“滋补保健”');chooseCode="02zbbj";itemIndex = 6;break;
					case 6:$('.gaomaonew-btn').html('点击跳转至“更多推荐”');chooseCode="02gdtj";itemIndex = 7;break;
					case 7:$('.gaomaonew-btn').html('点击跳转至“呼吸系统”');chooseCode="02hxxt";itemIndex = 0;break;
					default:break;
				}
				$('.gaomaonew .gaomaonew-btn').unbind();
				$('.gaomaonew .gaomaonew-btn').click(function(){
					$(".gaomaonew-cont2").empty();
					kernel.showLoading();
					getdatalist();
					tabscroll();
					changeClass();
					$dom.animate({  
			            scrollTop: scrollTop1
			        }, 0);
			        $('.gaomaonew .tab-section').css("position","fixed");
			        $(".baktitlebox").show();
				})
			}
		});
	}
	
	function tabscroll(){
		switch(itemIndex){
			case 0:myscroll.scrollTo(0, 0, 400);;break;
			case 1:myscroll.scrollTo(0, 0, 400);;break;
			case 2:myscroll.scrollTo(0, 0, 400);;break;
			case 3:myscroll.scrollTo(-120, 0, 400);;break;
			case 4:myscroll.scrollTo(-200, 0, 400);;break;
			case 5:myscroll.scrollTo(-285, 0, 400);;break;
			case 6:myscroll.scrollTo(-320, 0, 400);;break;
			case 7:myscroll.scrollTo(-340, 0, 400);;break;
			default:;
		}
	}
	function changeClass(){
		$('.tab-title-item').eq(itemIndex).addClass('cur').siblings().removeClass('cur');
		$('.tab-more-item').eq(itemIndex).addClass('cur').siblings().removeClass('cur');
	}
	$(".tab-more-item").click(function(){
		$(".tab-title").show();
		$(".tab-title-section").hide();
		$(".tab-more-section").hide();
		$(".tab-title-arrow").removeClass("open");
		itemIndex = $(this).index();
		chooseCode = typeCodeArr[itemIndex];
		$(".gaomaonew-cont2").empty();
		getdatalist();
		kernel.showLoading();
		tabscroll();
		changeClass();
	});

	$('.tab-title-item').click(function(){
		itemIndex = $(this).index();
		chooseCode = typeCodeArr[itemIndex];
		$(".gaomaonew-cont2").empty();
		getdatalist();
		kernel.showLoading();
		tabscroll();
		changeClass();

		$dom.animate({  
            scrollTop: scrollTop1
        }, 0);
        $('.gaomaonew .tab-section').css("position","fixed");
        $(".baktitlebox").show();
	})

	getdatalist()
	
	return {
		onload: function(force) {
			setAppTitle("高毛专区");
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});