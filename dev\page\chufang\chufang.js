'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/IScroll/IScroll'], function(module, kernel, jquery, IScroll) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.chufang");
	
	var myscroll=new IScroll(".tab-title",{scrollX: true, scrollbars: false, click: true });
	var scrollTop1, scrollTop2, scrollTop3, scrollTop4, scrollTop5, scrollTop6, scrollTop7, scrollTop8;
	

	$(".tab-title-arrow").click(function(){
		$(".tab-title").toggle();
		$(".tab-title-section").toggle();
		$(".tab-more-section").toggle();
		$(this).toggleClass("open");
	});

	$(".tab-btn1").click(function(){
        $dom.animate({  
            scrollTop: scrollTop1-43
        }, 0);
    });
  	$(".tab-btn2").click(function(){
        $dom.animate({  
            scrollTop: scrollTop2-45
        }, 0);
    });
    $(".tab-btn3").click(function(){
        $dom.animate({  
            scrollTop: scrollTop3-45
        }, 0);
    });
    $(".tab-btn4").click(function(){
        $dom.animate({  
            scrollTop: scrollTop4-45
        }, 0);  
    });
    $(".tab-btn5").click(function(){
        $dom.animate({  
            scrollTop: scrollTop5-45
        }, 0);  
    });
    $(".tab-btn6").click(function(){
        $dom.animate({  
            scrollTop: scrollTop6-45
        }, 0);  
    });
    $(".tab-btn7").click(function(){
        $dom.animate({  
            scrollTop: scrollTop7-45
        }, 0);  
    });
    $(".tab-btn8").click(function(){
        $dom.animate({  
            scrollTop: scrollTop8-45
        }, 0);  
    });

	$(".tab-more-section").click(function(){
		$(".tab-title").show();
		$(".tab-title-section").hide();
		$(".tab-more-section").hide();
		$(".tab-title-arrow").removeClass("open");
	});

    $dom.scroll(function(){
    	if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
    	$(".tab-title").show();
		$(".tab-title-section").hide();
		$(".tab-more-section").hide();
		$(".tab-title-arrow").removeClass("open");
		var sHtop = $dom.scrollTop()+50;//滚动条距离顶部的距离
		if(sHtop>=scrollTop1 && sHtop<scrollTop2){
			$(".tab-title-item").removeClass("cur");
			$(".tab-more-item ").removeClass("cur");
			$(".tab-btn1").addClass("cur");
			myscroll.scrollTo(0, 0, 400);
		}else if(sHtop>=scrollTop2 && sHtop<scrollTop3){
			$(".tab-title-item").removeClass("cur");
			$(".tab-more-item ").removeClass("cur");
			$(".tab-btn2").addClass("cur");
			myscroll.scrollTo(0, 0, 400);
		}else if(sHtop>scrollTop3 && sHtop<scrollTop4){
			$(".tab-title-item").removeClass("cur");
			$(".tab-more-item ").removeClass("cur");
			$(".tab-btn3").addClass("cur");
			myscroll.scrollTo(0, 0, 400);
		}else if(sHtop>scrollTop4 && sHtop<scrollTop5){
			$(".tab-title-item").removeClass("cur");
			$(".tab-more-item ").removeClass("cur");
			$(".tab-btn4").addClass("cur");
			myscroll.scrollTo(-120, 0, 400);
		}else if(sHtop>scrollTop5 && sHtop<scrollTop6){
			$(".tab-title-item").removeClass("cur");
			$(".tab-more-item ").removeClass("cur");
			$(".tab-btn5").addClass("cur");
			myscroll.scrollTo(-200, 0, 400);
		}else if(sHtop>scrollTop6 && sHtop<scrollTop7){
			$(".tab-title-item").removeClass("cur");
			$(".tab-more-item ").removeClass("cur");
			$(".tab-btn6").addClass("cur");
			myscroll.scrollTo(-285, 0, 400);
		}else if(sHtop>scrollTop7 && sHtop<scrollTop8){
			$(".tab-title-item").removeClass("cur");
			$(".tab-more-item ").removeClass("cur");
			$(".tab-btn7").addClass("cur");
			myscroll.scrollTo(-365, 0, 400);
		}else if(sHtop>scrollTop8){
			$(".tab-title-item").removeClass("cur");
			$(".tab-more-item ").removeClass("cur");
			$(".tab-btn8").addClass("cur");
			myscroll.scrollTo(-370, 0, 400);
		}
	});

	
    $.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=C_F_R_M&sort=1&limit=5",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId},
			dataType: "json",
			success: function(res) {
				var chufangTemp1 = " ";
				var chufangTarget1 = $(".chufang-cont1");
				
				for(var i=0;i<res.data.rows.length;i++){
					chufangTemp1+='<div class="chufang-item">\
								<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo">';
								/*if(res.data.rows[i].blackProductText!='' && res.data.rows[i].blackProductText!= null){
									chufangTemp1+='<span class="nofanmark">'+res.data.rows[i].blackProductText+'</span>';
								};*/
								chufangTemp1+='<img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'"></a>\
								<div class="pro-info">\
								<div class="pro-info-title"><a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="pro-title text-overflow">';
								if(res.data.rows[i].agent == 1){
									chufangTemp1+='<div class="dujia">独家</div>'
								}
								chufangTemp1+= res.data.rows[i].commonName+'</a></div><div class="pro-info-nomal text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
								chufangTemp1+='<div class="biaoqian">';
								for(var j in res.data.rows[i].tagList.slice(0,3)){
									switch(res.data.rows[i].tagList[j].uiType){
										case 1:chufangTemp1+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 2:chufangTemp1+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 3:chufangTemp1+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 4:chufangTemp1+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
										default:;break;
									}
								}
								chufangTemp1+='</div>'	
									if(res.data.rows[i].isControl==1){
										if(res.data.rows[i].isPurchase==true){
											chufangTemp1 += '<div class="pro-info-profits">';
											if(res.data.rows[i].uniformPrice){
												chufangTemp1 += '<span class="span1">控销价</span class="span2"><span class="span2">￥'+ res.data.rows[i].uniformPrice.toFixed(2)  +'</span>';
											}
											if(res.data.rows[i].suggestPrice){
												chufangTemp1 += '<span class="span1">零售价 </span><span class="span2">￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</span>';
											}
											if(res.data.rows[i].grossMargin){
												chufangTemp1 += '<span class="span3">毛利</span><span class="span4">'+ parseInt(res.data.rows[i].grossMargin) + '%</span>';
											}
											chufangTemp1 += '</div>';
											// if (res.data.rows[i].isSplit==0) {
												// chufangTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
											/*}else{
												chufangTemp1+='<div class="zhonbao-des"></div>';
											}*/
											chufangTemp1+='<div class="pro-info-bot">';
												if(res.data.rows[i].isControlPriceToMe==1){
													chufangTemp1+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
												}else{
													if(res.data.rows[i].priceType==1){
														chufangTemp1+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
													}else{
														chufangTemp1+='<div class="red bot-price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
													}
												}
													chufangTemp1+='<div class="handle">\
														<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
														<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'"  data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
														<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
													</div>';
												
											chufangTemp1+='</div>';
										}else{
											chufangTemp1+='<div class="pro-info-profits"></div><div class="pro-info-bot">\
												<div class="red bot-price"></div>\
												<div class="lackbuy">暂无购买权限</div>\
											</div>';
										}
									}else{
											chufangTemp1 += '<div class="pro-info-profits">';
											if(res.data.rows[i].uniformPrice){
												chufangTemp1 += '<span class="span1">控销价</span class="span2"><span class="span2">￥'+ res.data.rows[i].uniformPrice.toFixed(2)  +'</span>';
											}
											if(res.data.rows[i].suggestPrice){
												chufangTemp1 += '<span class="span1">零售价 </span><span class="span2">￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</span>';
											}
											if(res.data.rows[i].grossMargin){
												chufangTemp1 += '<span class="span3">毛利</span><span class="span4">'+ parseInt(res.data.rows[i].grossMargin) + '%</span>';
											}
											chufangTemp1 += '</div>';
											// if (res.data.rows[i].isSplit==0) {
												// chufangTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
											/*}else{
												chufangTemp1+='<div class="zhonbao-des"></div>';
											}*/
										chufangTemp1+='<div class="pro-info-bot">';
												if(res.data.rows[i].isControlPriceToMe==1){

													chufangTemp1+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
												}else{
													if(res.data.rows[i].priceType==1){
														chufangTemp1+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
													}else{
														chufangTemp1+='<div class="red bot-price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
													}
												}
												chufangTemp1+='<div class="handle">\
														<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
														<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'"  data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
														<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
													</div>';
											
										chufangTemp1+='</div>';
									}
									chufangTemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="a-link-mask"></a>\
								</div>';
								if(res.data.rows[i].status=="2"){
									chufangTemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
								};
								if(res.data.rows[i].markerUrl){
									chufangTemp1+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
								}
							chufangTemp1+='</div>';
				}
				chufangTarget1.empty().append(chufangTemp1);
				scrollTop1 = $(".chu-title1").offset().top;
				listAddMin();
			}
	});
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=C_F_X_N_X_G&sort=1&limit=8",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var chufangTemp2 = " ";
			var chufangTarget2 = $(".chufang-cont2");
			
			for(var i=0;i<res.data.rows.length;i++){
				chufangTemp2+='<div class="chufang-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					chufangTemp2 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							chufangTemp2 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							chufangTemp2 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							chufangTemp2 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					chufangTemp2+='</div></a>'
				}
				chufangTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							chufangTemp2+='<div class="dujia">独家</div>'
						}
					chufangTemp2+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';
				chufangTemp2+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				chufangTemp2+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:chufangTemp2+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:chufangTemp2+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:chufangTemp2+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:chufangTemp2+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				chufangTemp2+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									chufangTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										chufangTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										chufangTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// chufangTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										chufangTemp2+='<div class="zhonbao-des"></div>';
									}*/
										chufangTemp2+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								chufangTemp2+='<div class="price">暂无购买权限</div>';
								chufangTemp2 += '<div class="control-box"></div>';
								// chufangTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								chufangTemp2+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							chufangTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								chufangTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								chufangTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// chufangTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							chufangTemp2+='<div class="zhonbao-des"></div>';
						}*/
							chufangTemp2+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						chufangTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						chufangTemp2+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				chufangTemp2+='</div>';
			}
			chufangTarget2.append(chufangTemp2);
			scrollTop2 = $(".chu-title2").offset().top;

			
			rowAddMin();
			
		}
	});
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=C_F_T_N_B&sort=1&limit=8",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var chufangTemp3 = " ";
			var chufangTarget3 = $(".chufang-cont3");
			for(var i=0;i<res.data.rows.length;i++){
				chufangTemp3+='<div class="chufang-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					chufangTemp3 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							chufangTemp3 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							chufangTemp3 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							chufangTemp3 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					chufangTemp3+='</div></a>'
				}
				chufangTemp3+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							chufangTemp3+='<div class="dujia">独家</div>'
						}
					chufangTemp3+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				chufangTemp3+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				chufangTemp3+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:chufangTemp3+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:chufangTemp3+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:chufangTemp3+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:chufangTemp3+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				chufangTemp3+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									chufangTemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										chufangTemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										chufangTemp3+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// chufangTemp3+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										chufangTemp3+='<div class="zhonbao-des"></div>';
									}*/
										chufangTemp3+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								chufangTemp3+='<div class="price">暂无购买权限</div>';
								chufangTemp3 += '<div class="control-box"></div>';
								// chufangTemp3+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								chufangTemp3+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							chufangTemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								chufangTemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								chufangTemp3+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// chufangTemp3+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							chufangTemp3+='<div class="zhonbao-des"></div>';
						}*/
							chufangTemp3+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						chufangTemp3+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						chufangTemp3+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				chufangTemp3+='</div>';
			}
			chufangTarget3.append(chufangTemp3);
			scrollTop3 = $(".chu-title3").offset().top;
			rowAddMin();
			
			
		}
	});
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=C_F_X_Y_Z_T&sort=1&limit=8",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var chufangTemp4 = " ";
			var chufangTarget4 = $(".chufang-cont4");
			for(var i=0;i<res.data.rows.length;i++){
				chufangTemp4+='<div class="chufang-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					chufangTemp4 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							chufangTemp4 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							chufangTemp4 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							chufangTemp4 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					chufangTemp4+='</div></a>'
				}
				chufangTemp4+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							chufangTemp4+='<div class="dujia">独家</div>'
						}
					chufangTemp4+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				chufangTemp4+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				chufangTemp4+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:chufangTemp4+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:chufangTemp4+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:chufangTemp4+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:chufangTemp4+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				chufangTemp4+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									chufangTemp4+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										chufangTemp4+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										chufangTemp4+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// chufangTemp4+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										chufangTemp4+='<div class="zhonbao-des"></div>';
									}*/
										chufangTemp4+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								chufangTemp4+='<div class="price">暂无购买权限</div>';
								chufangTemp4 += '<div class="control-box"></div>';
								// chufangTemp4+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								chufangTemp4+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							chufangTemp4+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								chufangTemp4+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								chufangTemp4+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// chufangTemp4+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							chufangTemp4+='<div class="zhonbao-des"></div>';
						}*/
							chufangTemp4+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						chufangTemp4+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						chufangTemp4+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				chufangTemp4+='</div>';
			}
			chufangTarget4.append(chufangTemp4);
			scrollTop4 = $(".chu-title4").offset().top;

			rowAddMin();
		
			
		}
	});
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=C_F_F_S_G_T&sort=1&limit=8",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var chufangtemp5 = " ";
			var chufangTarget5 = $(".chufang-cont5");
			for(var i=0;i<res.data.rows.length;i++){
				chufangtemp5+='<div class="chufang-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					chufangtemp5 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							chufangtemp5 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							chufangtemp5 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							chufangtemp5 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					chufangtemp5+='</div></a>'
				}
				chufangtemp5+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							chufangtemp5+='<div class="dujia">独家</div>'
						}
					chufangtemp5+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				chufangtemp5+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				chufangtemp5+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:chufangtemp5+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:chufangtemp5+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:chufangtemp5+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:chufangtemp5+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				chufangtemp5+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									chufangtemp5+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										chufangtemp5+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										chufangtemp5+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// chufangtemp5+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										chufangtemp5+='<div class="zhonbao-des"></div>';
									}*/
										chufangtemp5+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								chufangtemp5+='<div class="price">暂无购买权限</div>';
								chufangtemp5 += '<div class="control-box"></div>';
								// chufangtemp5+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								chufangtemp5+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							chufangtemp5+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								chufangtemp5+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								chufangtemp5+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// chufangtemp5+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							chufangtemp5+='<div class="zhonbao-des"></div>';
						}*/
							chufangtemp5+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						chufangtemp5+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						chufangtemp5+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				chufangtemp5+='</div>';
			}
			chufangTarget5.append(chufangtemp5);
			scrollTop5 = $(".chu-title5").offset().top;
			rowAddMin();
		
			
		}
	});
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=C_F_C_W&sort=1&limit=8",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var chufangTemp6 = " ";
			var chufangTarget6 = $(".chufang-cont6");
			for(var i=0;i<res.data.rows.length;i++){
				chufangTemp6+='<div class="chufang-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					chufangTemp6 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							chufangTemp6 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							chufangTemp6 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							chufangTemp6 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					chufangTemp6+='</div></a>'
				}
				chufangTemp6+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							chufangTemp6+='<div class="dujia">独家</div>'
						}
					chufangTemp6+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';
				chufangTemp6+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				chufangTemp6+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:chufangTemp6+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:chufangTemp6+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:chufangTemp6+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:chufangTemp6+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				chufangTemp6+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									chufangTemp6+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										chufangTemp6+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										chufangTemp6+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// chufangTemp6+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										chufangTemp6+='<div class="zhonbao-des"></div>';
									}*/
										chufangTemp6+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								chufangTemp6+='<div class="price">暂无购买权限</div>';
								chufangTemp6 += '<div class="control-box"></div>';
								// chufangTemp6+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								chufangTemp6+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							chufangTemp6+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								chufangTemp6+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								chufangTemp6+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// chufangTemp6+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							chufangTemp6+='<div class="zhonbao-des"></div>';
						}*/
							chufangTemp6+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						chufangTemp6+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						chufangTemp6+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				chufangTemp6+='</div>';
			}
			chufangTarget6.append(chufangTemp6);
			scrollTop6 = $(".chu-title6").offset().top;

			rowAddMin();
		
			
		}
	});
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=C_F_H_X&sort=1&limit=8",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var chufangtemp7 = " ";
			var chufangTarget7 = $(".chufang-cont7");
			for(var i=0;i<res.data.rows.length;i++){
				chufangtemp7+='<div class="chufang-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					chufangtemp7 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							chufangtemp7 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							chufangtemp7 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							chufangtemp7 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					chufangtemp7+='</div></a>'
				}
				chufangtemp7+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							chufangtemp7+='<div class="dujia">独家</div>'
						}
					chufangtemp7+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				chufangtemp7+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				chufangtemp7+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:chufangtemp7+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:chufangtemp7+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:chufangtemp7+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:chufangtemp7+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				chufangtemp7+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									chufangtemp7+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										chufangtemp7+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										chufangtemp7+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// chufangtemp7+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										chufangtemp7+='<div class="zhonbao-des"></div>';
									}*/
										chufangtemp7+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								chufangtemp7+='<div class="price">暂无购买权限</div>';
								chufangtemp7 += '<div class="control-box"></div>';
								// chufangtemp7+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								chufangtemp7+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							chufangtemp7+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								chufangtemp7+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								chufangtemp7+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// chufangtemp7+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							chufangtemp7+='<div class="zhonbao-des"></div>';
						}*/
							chufangtemp7+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						chufangtemp7+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						chufangtemp7+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				chufangtemp7+='</div>';
			}
			chufangTarget7.append(chufangtemp7);
			scrollTop7 = $(".chu-title7").offset().top;
			rowAddMin();
		
			
		}
	});
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=C_F_Q_T&sort=1&limit=8",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var chufangtemp8 = " ";
			var chufangTarget8 = $(".chufang-cont8");
			for(var i=0;i<res.data.rows.length;i++){
				chufangtemp8+='<div class="chufang-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					chufangtemp8 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							chufangtemp8 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							chufangtemp8 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							chufangtemp8 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					chufangtemp8+='</div></a>'
				}
				chufangtemp8+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							chufangtemp8+='<div class="dujia">独家</div>'
						}
					chufangtemp8+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';
				chufangtemp8+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				chufangtemp8+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:chufangtemp8+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:chufangtemp8+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:chufangtemp8+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:chufangtemp8+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				chufangtemp8+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									chufangtemp8+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										chufangtemp8+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										chufangtemp8+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// chufangtemp8+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										chufangtemp8+='<div class="zhonbao-des"></div>';
									}*/
										chufangtemp8+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								chufangtemp8+='<div class="price">暂无购买权限</div>';
								chufangtemp8 += '<div class="control-box"></div>';
								// chufangtemp8+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								chufangtemp8+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							chufangtemp8+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								chufangtemp8+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								chufangtemp8+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// chufangtemp8+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							chufangtemp8+='<div class="zhonbao-des"></div>';
						}*/
							chufangtemp8+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						chufangtemp8+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						chufangtemp8+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				chufangtemp8+='</div>';
			}
			chufangTarget8.append(chufangtemp8);
			scrollTop8 = $(".chu-title8").offset().top;

			rowAddMin();
		
			
		}
	});
	
	return {
		onload: function(force) {
			setAppTitle("热销处方药");
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});