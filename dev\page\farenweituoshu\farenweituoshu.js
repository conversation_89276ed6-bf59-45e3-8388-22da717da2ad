'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	    var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
		var $dom = $("#page>.content>.farenweituoshu");
		// backTop(thispage);

		var obj = {title:'保存图片',action:'ybmaction://savephoto?name=template.jpg&loading=true&url='+apiUrl+'/static/public/events/0401/template.jpg'}
		if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.setRightMenu.postMessage(obj); //ios
			}catch(err){
				
			}
		}else{
			try{
				window.hybrid.setRightMenu('保存图片','ybmaction://savephoto?name=template.jpg&loading=true&url='+apiUrl+'/static/public/events/0401/template.jpg')
			}catch(err){
				
			}
		}
		
		return {
			onload: function(force) {
				/*setAppTitle("高毛专区活动规则");
				$dom.animate({  
		            scrollTop: 0
		        }, 0);*/
			},
			onloadend: function() {
				//this page is open

			},
	        onunload: function() {
	            //leveing this page
	            $("#to_top").hide();
	        },
	        onunloadend: function() {
	            //left this page
	        }
	        // 除以上事件外还可包含以下属性
			// * onleftmenuclick 左上角dom点击事件
			// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
			// * onrightmenuclick 右上角dom点击事件
			// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
		};
	
});