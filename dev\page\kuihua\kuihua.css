#page > .content > .kuihua {
  overflow-y: auto;
  border-top: none;
  background: #dc1637;
}
#page > .content > .kuihua .kuihua-list .kuihua-item {
  position: relative;
}
#page > .content > .kuihua .kuihua-list .kuihua-item.kuihua-item4 {
  margin-bottom: 8px;
}
#page > .content > .kuihua .kuihua-list .kuihua-item .kuihua-btn {
  position: absolute;
  left: 260px;
  top: 65px;
}
#page > .content > .kuihua .kuihua-list .kuihua-item .kuihua-btn.kuihua-btn2 {
  top: 160px;
}
#page > .content > .kuihua .kuihua-list .kuihua-item .kuihua-btn .handle {
  height: 27px;
}
#page > .content > .kuihua .kuihua-list .kuihua-item .kuihua-btn .handle .inp-total {
  display: inline-block;
  width: 35px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  opacity: 1;
  color: #333;
  border: none;
  font-size: 12px;
  background: #fff6e5;
  float: right;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
#page > .content > .kuihua .kuihua-list .kuihua-item .kuihua-btn .handle .min {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 15px 0 0 15px;
  color: #666;
  float: right;
}
#page > .content > .kuihua .kuihua-list .kuihua-item .kuihua-btn .handle .add {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 0 15px 15px 0;
  color: #666;
  float: right;
}
@media (max-width: 359px) {
  #page > .content > .kuihua .kuihua-list .kuihua-item .kuihua-btn {
    top: 53px;
    left: 215px;
  }
  #page > .content > .kuihua .kuihua-list .kuihua-item .kuihua-btn.kuihua-btn2 {
    top: 133px;
  }
}
