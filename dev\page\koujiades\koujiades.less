#page>.content>.koujiades{
    overflow-y:auto; 
    background: #fafafa;
    border-top: none;
    .koujia-cont{
        background: #fff;
        .koujia-head{
            display: block;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
            color: #666;
            .more{
                float: right;
                padding-right: 10px;
                padding-top: 12px;
            }
        }
        &.koujia-cont2{
            margin-top: 10px;
        }
    }
    .koujia-item{
        display: inline-block;
        width: 50%;
        float: left;
        margin-bottom: 5px;
        position: relative;
        .photo{
            display: block;
            margin: 8px;
            padding: 8px;
            border: 1px solid #eee;
            border-radius: 5px;
        }
        .name{
            padding: 0 8px;
            color: #000;
            height: 14px;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .control-box{
            padding:0 4px;
            height:16px;
            margin-top:5px;
            span{
                font-size:12px;
                padding:2px 4px;
                line-height:12px;
                border-radius:8px;
                color:#fff;
                display:inline-block;
            }
            i{
                font-size:12px;
                line-height:16px;
                display:inline-block;
            }
            .control-price{
                float:left;
                span{
                    background:#3d4652;
                }
            }
            .control-gross{
                float:left;   
                span{
                    background:#ff7176;
                    margin-left:6px;
                }
            }
        }
        .price{
            color: #000;
            padding: 8px;
            height: 30px;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .notprice{
            color: #000;
            padding: 8px;
            height: 30px;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .normol{
            color: #999;
            padding: 0 8px;
            height: 14px;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 12px;
        }
        .markerUrl{
            position: absolute;
            width: 35px;
            height: 48px;
            top: 9px;
            left: 9px;
        }
        .gone-icon{
            display: block;
            position: absolute;
            width: 60%;
            height: 65%;
            background: url(./images/<EMAIL>) center center no-repeat;
            background-size: 50%;
            top: 13%;
            left: 20%;
            right: 20%;
            bottom: 0;
        }

    }
    .bot-tips{
        font-size: 12px;
        padding: 10px 5px;
    }
}
@media(max-width:321px){
    .control-box{
        height:36px!important;
        .control-price i{
            margin-left:10px!important;
        }
        .control-gross i{
            margin-left:13px!important;
        }
        .control-gross span{
            margin-left:0!important;
        }
    }
}