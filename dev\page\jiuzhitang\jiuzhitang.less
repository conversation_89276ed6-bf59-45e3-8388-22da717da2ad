#page>.content>.jiuzhitang{
	overflow-y:auto; 
	border-top: none;
	background: #377c36;
	padding:0;
    .recom-med{
    	position:relative;
    	.med-link{
    		position:absolute;
    		left:0;
    		top:0;
    		height:95px;
    		z-index:5;
    		width:100%;
    		display:block;
    	}
		.med-mesg{
			width:52%;
			position:absolute;
			right:0;
			top:5px;
			.med-title{
				font-size:18px;
				line-height:20px;
				height:20px;
				font-weight:700;
				color:#000;
			}
			.med-spec{
				font-size:14px;
				line-height: 16px;
				height:16px;
				color:#666;
				margin:7px 0 10px;
			}
			.med-fob{
				font-size:20px;
				line-height:22px;
				height:22px;
				color:#ec1346;
				font-weight:700;
			}
			.handle{
				margin-top:20px;
				width:97px;
	            .inp-total{
	                display: inline-block;
	                width: 35px;
	                height: 31px;
	                text-align: center;
	                line-height: 31px;
	                opacity: 1;
	                color: #333;
	                border: none;
	                font-size: 12px;
	                background: #efefef;
	                float: right;
	                border-top: 1px solid #ccc;
	                border-bottom: 1px solid #ccc;
	            }
	            .min,.add{
	                display: inline-block;
	                width: 31px;
	                height: 31px;
	                text-align: center;
	                line-height: 28px;
	                font-size: 24px;
	                color: #fff;
	                float: right;
	                background: #00dc82;
	            }
	            .min{
	                border-radius: 15px 0 0 15px;
	            }
	            .add{
	                border-radius: 0 15px 15px 0;
	            }
	        }
		}
    }
    .tipstext{
    	margin: 2%;
    	width: 96%;
    	color: #fff;
    	line-height: 22px;
    }
    .jiuzhitang-list{
    	min-height: 300px;
    	padding-top:5px;
    	.jiuzhitang-cont{
    		margin: 0 0.5%;

    		.jiuzhitang-item{
				margin: 0.5%;
				width: 49%;
				background: #fff;
				border-radius: 4px;
				float: left;
				position: relative;
				.photo{
					display: block;
					padding: 10px;
					position:relative;
					img{
						height: 140px;
					}
					.control-box{
			            padding:0 5px;
			            position:absolute;
			            left:0;
			            bottom:0;
			            span{
			                font-size:12px;
			                padding:2px 2px;
			                line-height:12px;
			                color:#fff;
			                border-radius:4px 0 0 4px;
			                background:#2b343f;
			                display:inline-block;
			            }
			            i{
			                font-size:12px;
			                display:inline-block;
			                background:#fff;
			                color:#666;
			                border:1px solid #2b343f;
			                padding:0 2px;
			                border-radius: 0 4px 4px 0;
			            }
			            .control-price{
			                float:left;
	                        display: flex;
	                        i{
	                        	flex:1;
	                        	line-height:14px;
	                        }
			            }
			            .control-gross{
			                float:left; 
			                display: flex;
	                        i{
	                        	flex:1;
	                        	line-height:14px;
	                        }  
			                span{
			                    margin-left:3px;
			                }
			            }
			        }
				}
				.commonName{
					height:24px;
					display:block;
					line-height:24px;
					padding-left:5px;
					.dujia{
						float:left;
						color:#fff;
						padding:1px 2px;
						font-size:12px;
						line-height:12px;
						border-radius:3px;
						font-weight:400;
						background:#27adff;
						margin-top:5px;
						margin-right:3px;
					}
					.name{
						color: #333;
						font-size:14px;
						line-height:24px;
					}
				}
				.price{
					color: #ff2400;
					padding: 5px;
					font-weight: bold;
				}
				.norms{
					margin:0 5px 3px;
					font-size: 12px;
					line-height:14px;
					height:14px;
					color: #999;
					.zhonbao-des{
						margin-left:5px;
		                display:inline;
			        }
				}
				.biaoqian{
					height:20px;
					display:flex;
					flex-direction:rows;
					align-items:center;
					padding-left: 5px;
					div{
						padding:1px 3px;
						margin-right:5px;
						line-height:12px;
						font-size:12px;
						color:#fff;
						border-radius:3px;
						display:inline-block;
						&.quan{
							background:#ff0000;
						}
						&.linqi{
							background:#ff9400;
						}
						&.normal{
							color:#ff0e0e;
							padding:0px 2px;
							border:1px solid #ff9595;
						}
						&.fubiao{
							background:#e4e4e4;
							color:#333;
						}
					}
				}
				.handle{
					width: 94%;
					margin: 0px auto 8px;
					height: 27px;
					.inp-total{
						display: inline-block;
                        width: 35px;
                        height: 25px;
                        text-align: center;
                        line-height: 25px;
                        opacity: 1;
                        color: #333;
                        border: none;
                        font-size: 12px;
                        background: #efefef;
                        float: right;
                        border-top: 1px solid #ccc;
                        border-bottom: 1px solid #ccc;
					}
					.min{
                        display: inline-block;
                        width: 25px;
                        height: 25px;
                        text-align: center;
                        line-height: 22px;
                        font-size: 24px;
                        border: 1px solid #ccc;
                        border-radius: 15px 0 0 15px;
                        color: #666;
                        float: right;
                    }
                    .add{
                        display: inline-block;
                        width: 25px;
                        height: 25px;
                        text-align: center;
                        line-height: 22px;
                        font-size: 24px;
                        border: 1px solid #ccc;
                        border-radius: 0 15px 15px 0;
                        color: #666;
                        float: right;
                    }
					
				}
				.handle-lon{
                    width: 94%;
					margin: 0 auto 8px;
					height: 27px;
                    .add-lon{
                        display: inline-block;
                        width: 25px;
                        height: 25px;
                        text-align: center;
                        line-height: 22px;
                        font-size: 24px;
                        border: 1px solid #ccc;
                        border-radius: 50%;
                        color: #666;
                        float: right;
                    }
                }
				.gone-icon{
					display: block;
	                position: absolute;
	                width: 100%;
	                height: 65%;
	                background: url(./images/<EMAIL>) center center no-repeat;
	                background-size: 30%;
	                top: 0;
	                left: 0;
	                right: 0;
	                bottom: 0;
	            }
	            .markerUrl{
	            	position: absolute;
	                width: 40px;
	                height: 48px;
	                top: -2px;
	                left: -1px;
	            }

    		}
    	}
    }
    .detail-box{
    	background:rgba(0,0,0,0.5);
    	position:fixed;
    	right: 0;
        left: 0;
        bottom: 0;
        top: 0;
        display:none;
        overflow:hidden;
        .detail-content{
        	width:100%;
        	position:absolute;
        	left:0;
        	top:8%;
        	// bottom:6%;
        	padding-bottom:10px;
        	.detail-msg-box{
        		background: url('/static/public/events/0512/jzt10.png') left top repeat;
	        	background-size:100%;
        		.detail-msg{
	        		width:88.9%;
	        		color:#333;
	        		padding:0 10px;
	        		margin:0 auto;
	        		overflow-y: auto;
	        		font-size:14px;
	        		h2,p{
						margin:0;
						padding:0;
						font-weight:normal;
					}
					h2{
						line-height:16px;
						font-weight:700;
						margin-top:15px;
						color:#000;
					}
					p{
						line-height:20px;
						&.zengp{
							color:#db2626;
							// font-style:italic;
						}
					}
					.warning{
						color:#666;
						font-size:14px;
						padding:20px 0 10px;
						text-align:center;
					}
					table{
						margin-top:5px;
						td{
							width:33%;
							text-align:center;
							line-height:20px;
							padding:5px;
							background: #fff;
						}
						tr:first-child{
							td{
								height:60px;
								background:#b1e5d1;
								color:#4c776d;
								font-weight:600;
							}
						}
						tr td:last-child{
							border-left:1px solid #d4f6e2;
						}
						tr:last-child td{
							border-top:1px solid #d4f6e2;
						}
					}
	        	}
        	}
        	
        }
    }
}
 @media(max-width:480px) {
	#page > .content > .jiuzhitang .jiuzhitang-list .jiuzhitang-cont .jiuzhitang-item .photo img {
	    height: 166px;
	}
}
@media(max-width:414px) {
	#page > .content > .jiuzhitang .jiuzhitang-list .jiuzhitang-cont .jiuzhitang-item .photo img {
	    height: 159px;
	} 
	 #page > .content > .jiuzhitang .detail-box .detail-content .detail-msg{
		max-height:480px;
	}  
}
@media(max-width:412px) {
	#page > .content > .jiuzhitang .jiuzhitang-list .jiuzhitang-cont .jiuzhitang-item .photo img {
	    height: 158px;
	}
}
@media(max-width:375px) {
	#page > .content > .jiuzhitang .jiuzhitang-list .jiuzhitang-cont .jiuzhitang-item .photo img {
	    height: 162px;
	}
	  #page > .content > .jiuzhitang .detail-box .detail-content .detail-msg{
	 		max-height:440px;
	 	}  
}
@media(max-width:360px) {
	#page > .content > .jiuzhitang .jiuzhitang-list .jiuzhitang-cont .jiuzhitang-item .photo img {
	    height: 162px;
	}
	  #page > .content > .jiuzhitang .detail-box .detail-content .detail-msg{
			max-height:400px;
		}  
}
@media(max-width:350px) {
	#page > .content > .jiuzhitang .jiuzhitang-list .jiuzhitang-cont .jiuzhitang-item .photo img {
	    height: 155px;
	}
	#page > .content > .jiuzhitang .recom-med .med-mesg .handle {
	    margin-top:8px;
	}
	
	#page > .content > .jiuzhitang .jiuzhitang-list .jiuzhitang-cont .jiuzhitang-item .photo .control-box{
	    height:40px;
	    span{
	    	width:54px;
	    	text-align:center;
	    }
	    i{
			text-align:center;
			padding:0 8px;
		}
		.control-gross span{
			margin-left:0;
		}
		.control-gross{
			margin-top:3px;
		}
	}
}
@media(max-width:320px) {
	#page > .content > .jiuzhitang .jiuzhitang-list .jiuzhitang-cont .jiuzhitang-item .photo img {
	    height: 135px;
	}
} 