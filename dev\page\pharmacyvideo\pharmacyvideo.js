'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.pharmacyvideo");
	var dataId;

	function autoAddZero(n){
   		return  n < 10 ? '0'+ n : n;
   	}

   	function timeFormat(timestr){
   		if(timestr == null) return '';
   		timestr = typeof timestr == "number" ? timestr : timestr.replace(/-/g, "/");
		var nowtime = new Date(timestr);
		var year = nowtime.getFullYear();
		var month = nowtime.getMonth();
		var day = nowtime.getDate();
		return year+'-'+autoAddZero(month+1)+'-'+autoAddZero(day);
   	}
   			
	return {
		onload: function(force) {
			setAppTitle("药学院");
			dataId = kernel.location.args.videoId; 
			 $.ajax({
				type: "POST",
				url: apiUrl+"/app/yaoDetail/queryYaoDetail",
				headers : {'version':version,'terminalType':terminalType},
				data: {"merchantId":merchantId,id:dataId},
				dataType: "json",
				success: function(res) {
					var pharmacyvideoTarget=$(".pharmacyvideo-content")
					var pharmacyvideoTemp = '';
					if(res.yaoDetail.content != '' && res.yaoDetail.content != null){
						pharmacyvideoTemp +='<div class="zixun-content">\
												<div class="title">'+res.yaoDetail.title+'</div>\
												<div class="time-box">\
													<div class="time">发布时间：'+timeFormat(res.yaoDetail.createTime)+'</div>\
													<div class="watch-time">浏览次数：'+res.yaoDetail.pageviewShow+'次</div>\
												</div>\
											</div>'
						pharmacyvideoTemp += res.yaoDetail.content
						pharmacyvideoTarget.empty().append(pharmacyvideoTemp);
						$("p").css({'padding':'0 10px'})
						return;
					}
					pharmacyvideoTemp='<div class="video-box"><video src="'+res.yaoDetail.url+'" poster="'+hostUrl+res.yaoDetail.viewImage+'"></video><a href="javascript:;" class="model-box"></a></div>\
						<div class="video-mesg">\
							<div class="title">'+res.yaoDetail.title+'</div>\
							<div class="time-box">\
								<div class="publictime">发布时间：'+timeFormat(res.yaoDetail.createTime)+'</div>\
								<div class="watchtimes">播放次数：'+res.yaoDetail.pageviewShow+'次</div>\
							</div>\
						</div>\
						<div class="content">\
							<div class="title"> <span class="token"></span>视频内容</div>\
							<div class="detail">'+res.yaoDetail.description+'</div>\
						</div>'
					pharmacyvideoTarget.empty().append(pharmacyvideoTemp);

					var isIphone = navigator.userAgent.indexOf('iPhone') >= 0;
					$('video').load();
					if(isIphone){
						$('video').attr("controls",'controls')
					}else{
						$(".model-box").show();
						$(".model-box").unbind();
						$(".model-box").bind("click",function(){
							$(this).hide();

							var vd = this.parentNode.querySelector("video");
							var videosrc = ($(vd).attr('src'))
							if(vd.paused){
								// $(vd).attr("src",videosrc)
								vd.play();
							}else{
								vd.pause();
							}
						})
					}
				}		
			});	
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
            if(document.querySelector("video")) document.querySelector("video").pause();
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});