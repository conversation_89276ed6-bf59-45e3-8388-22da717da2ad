'use strict';
define(['module', 'common/kernel/kernel', 'common/zepto/zepto'], function(module, kernel, zepto) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);


	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=K_J_S_P&sort=1&limit=6",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var koujiaTemp1 = " ";
			var koujiaTarget1 = $(".koujia-list1");
			
			for(var i=0;i<res.data.rows.length;i++){
				koujiaTemp1+='<a class="koujia-item" href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'"><div class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'"></div><div class="name">'+res.data.rows[i].commonName+'</div>';
				koujiaTemp1 += '<div class="control-box">';
				if(res.data.rows[i].isControl==1){
						if(res.data.rows[i].isPurchase==true){
							if(res.data.rows[i].uniformPrice){
								koujiaTemp1 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
							}
							if(res.data.rows[i].suggestPrice){
											koujiaTemp1 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
										};
							if(res.data.rows[i].grossMargin){
								koujiaTemp1 += '<div class="control-gross"><span>毛利</span><i>' + parseFloat(res.data.rows[i].grossMargin).toFixed(2) +'%</i></div>'
							}
							koujiaTemp1 += '</div>';
							if(res.data.rows[i].priceType==1){
								koujiaTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								koujiaTemp1+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
							
								
						}else{
							koujiaTemp1 += '</div>';
							koujiaTemp1+='<div class="price">暂无购买权限</div>';
						}
				}else{
					if(res.data.rows[i].uniformPrice){
								koujiaTemp1 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
					}
					if(res.data.rows[i].suggestPrice){
											koujiaTemp1 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
										};
					if(res.data.rows[i].grossMargin){
						koujiaTemp1 += '<div class="control-gross"><span>毛利</span><i>' + parseFloat(res.data.rows[i].grossMargin).toFixed(2) +'%</i></div>'
					}
					koujiaTemp1 += '</div>';
					if(res.data.rows[i].priceType==1){
						koujiaTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
					}else{
						koujiaTemp1+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
					}	
					
				}
				koujiaTemp1+='<div class="normol">'+res.data.rows[i].spec+'</div>';
				if(res.data.rows[i].status=="2"){
					koujiaTemp1+='<span class="gone-icon"></span>';
				};
				if(res.data.rows[i].markerUrl){
					koujiaTemp1+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
				}
				koujiaTemp1+='</a>';
			}
			koujiaTarget1.empty().append(koujiaTemp1);

			
			
		}
	});
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=D_J_S_P&sort=1&limit=6",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var koujiaTemp2 = " ";
			var koujiaTarget2 = $(".koujia-list2");
			
			for(var i=0;i<res.data.rows.length;i++){
				koujiaTemp2+='<a class="koujia-item" href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'"><div class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'"></div><div class="name">'+res.data.rows[i].commonName+'</div>';
				koujiaTemp2 += '<div class="control-box">';
				if(res.data.rows[i].isControl==1){
						if(res.data.rows[i].isPurchase==true){
							if(res.data.rows[i].uniformPrice){
								koujiaTemp2 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
							}
							if(res.data.rows[i].suggestPrice){
											koujiaTemp2 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
										};
							if(res.data.rows[i].grossMargin){
								koujiaTemp2 += '<div class="control-gross"><span>毛利</span><i>' + parseFloat(res.data.rows[i].grossMargin).toFixed(2) +'%</i></div>'
							}
							koujiaTemp2 += '</div>';
							if(res.data.rows[i].priceType==1){
								koujiaTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								koujiaTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
							
								
						}else{
							koujiaTemp2 += '</div>';
							koujiaTemp2+='<div class="price">暂无购买权限</div>';
						}
				}else{
					if(res.data.rows[i].uniformPrice){
						koujiaTemp2 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
					}
					if(res.data.rows[i].suggestPrice){
											koujiaTemp2 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
										};
					if(res.data.rows[i].grossMargin){
						koujiaTemp2 += '<div class="control-gross"><span>毛利</span><i>' + parseFloat(res.data.rows[i].grossMargin).toFixed(2) +'%</i></div>'
					}
					koujiaTemp2 += '</div>';
					if(res.data.rows[i].priceType==1){
						koujiaTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
					}else{
						koujiaTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
					}	
					
				}
				koujiaTemp2+='<div class="normol">'+res.data.rows[i].spec+'</div>';
				if(res.data.rows[i].status=="2"){
					koujiaTemp2+='<span class="gone-icon"></span>';
				};
				if(res.data.rows[i].markerUrl){
					koujiaTemp2+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
				}
				koujiaTemp2+='</a>';
			}
			koujiaTarget2.empty().append(koujiaTemp2);

			
			
		}
	});
	
	return {
		onload: function(force) {
			setAppTitle('控价规则')
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});