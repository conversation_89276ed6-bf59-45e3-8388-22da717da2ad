'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	    var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
		//var $dom = $("#page>.content>.feedbackrule");
		backTop(thispage);
		return {
			onload: function(force) {
				// setAppTitle("高毛专区活动规则");
				
			},
			onloadend: function() {
				//this page is open

			},
	        onunload: function() {
	            //leveing this page
	            $("#to_top").hide();
	        },
	        onunloadend: function() {
	            //left this page
	        }
	        // 除以上事件外还可包含以下属性
			// * onleftmenuclick 左上角dom点击事件
			// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
			// * onrightmenuclick 右上角dom点击事件
			// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
		};
	
});