'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.active");

	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=01hxxt&sort=1&limit=1000",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var activeTemp = " ";
			var activeTarget = $(".active-cont");
			for(var i=0;i<res.data.rows.length;i++){
				activeTemp+='<div class="active-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					activeTemp += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							activeTemp += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							activeTemp += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							activeTemp += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					activeTemp+='</div></a>'
				}
				activeTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							activeTemp+='<div class="dujia">独家</div>'
						}
					activeTemp+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';
				activeTemp+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				activeTemp+='<div class="biaoqian">';
				if(res.data.rows[i].tagList){
					for(var j in res.data.rows[i].tagList.slice(0,3)){
						switch(res.data.rows[i].tagList[j].uiType){
							case 1:activeTemp+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 2:activeTemp+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 3:activeTemp+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 4:activeTemp+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
							default:;break;
						}
					}
				}
				activeTemp+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									activeTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										activeTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										activeTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// activeTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										activeTemp+='<div class="zhonbao-des"></div>';
									}*/
										activeTemp+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								activeTemp+='<div class="price">暂无购买权限</div>';
								activeTemp += '<div class="control-box"></div>';
								// activeTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								activeTemp+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							activeTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								activeTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								activeTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// activeTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							activeTemp+='<div class="zhonbao-des"></div>';
						}*/
							activeTemp+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						activeTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						activeTemp+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				activeTemp+='</div>';
			}
			activeTarget.empty().append(activeTemp);
			$(".loading-tips").hide();
			rowAddMin();
			
		}
	});
	function getDataList(code){
		if(code=="01hxxt"){
			$(".actab-img1").attr("src","/static/public/events/1111/sabc1.jpg");
			$(".actab-img2").attr("src","/static/public/events/1111/sab2.jpg");
			$(".actab-img3").attr("src","/static/public/events/1111/sab3.jpg");
			$(".actab-img4").attr("src","/static/public/events/1111/sab4.jpg");
			$(".actab-img5").attr("src","/static/public/events/1111/sab5.jpg");
			$(".acmorebtn").hide();
			$(".acmorebtn1").show();
		};
		if(code=="01zbbj"){
			$(".actab-img1").attr("src","/static/public/events/1111/sab1.jpg");
			$(".actab-img2").attr("src","/static/public/events/1111/sabc2.jpg");
			$(".actab-img3").attr("src","/static/public/events/1111/sab3.jpg");
			$(".actab-img4").attr("src","/static/public/events/1111/sab4.jpg");
			$(".actab-img5").attr("src","/static/public/events/1111/sab5.jpg");
			$(".acmorebtn").hide();
			$(".acmorebtn2").show();
		};
		if(code=="01kjxy"){
			$(".actab-img1").attr("src","/static/public/events/1111/sab1.jpg");
			$(".actab-img2").attr("src","/static/public/events/1111/sab2.jpg");
			$(".actab-img3").attr("src","/static/public/events/1111/sabc3.jpg");
			$(".actab-img4").attr("src","/static/public/events/1111/sab4.jpg");
			$(".actab-img5").attr("src","/static/public/events/1111/sab5.jpg");
			$(".acmorebtn").hide();
			$(".acmorebtn3").show();
		};
		if(code=="01mbqt"){
			$(".actab-img1").attr("src","/static/public/events/1111/sab1.jpg");
			$(".actab-img2").attr("src","/static/public/events/1111/sab2.jpg");
			$(".actab-img3").attr("src","/static/public/events/1111/sab3.jpg");
			$(".actab-img4").attr("src","/static/public/events/1111/sabc4.jpg");
			$(".actab-img5").attr("src","/static/public/events/1111/sab5.jpg");
			$(".acmorebtn").hide();
			$(".acmorebtn4").show();
		};
		if(code=="01wyy"){
			$(".actab-img1").attr("src","/static/public/events/1111/sab1.jpg");
			$(".actab-img2").attr("src","/static/public/events/1111/sab2.jpg");
			$(".actab-img3").attr("src","/static/public/events/1111/sab3.jpg");
			$(".actab-img4").attr("src","/static/public/events/1111/sab4.jpg");
			$(".actab-img5").attr("src","/static/public/events/1111/sabc5.jpg");
			$(".acmorebtn").hide();
		};
		kernel.showLoading();
		$dom.animate({  
            scrollTop: titTop
        }, 0);
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId="+code+"&sort=1&limit=1000",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId},
			dataType: "json",
			success: function(res) {
				var activeTemp = " ";
				var activeTarget = $(".active-cont");
				for(var i=0;i<res.data.rows.length;i++){
				activeTemp+='<div class="active-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					activeTemp += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							activeTemp += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							activeTemp += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							activeTemp += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					activeTemp+='</div></a>'
				}
				activeTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							activeTemp+='<div class="dujia">独家</div>'
						}
					activeTemp+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';
				activeTemp+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				activeTemp+='<div class="biaoqian">';
				if(res.data.rows[i].tagList){
					for(var j in res.data.rows[i].tagList.slice(0,3)){
						switch(res.data.rows[i].tagList[j].uiType){
							case 1:activeTemp+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 2:activeTemp+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 3:activeTemp+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
							case 4:activeTemp+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
							default:;break;
						}
					}
				}
				activeTemp+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									activeTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										activeTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										activeTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// activeTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										activeTemp+='<div class="zhonbao-des"></div>';
									}*/
										activeTemp+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								activeTemp+='<div class="price">暂无购买权限</div>';
								activeTemp += '<div class="control-box"></div>';
								// activeTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								activeTemp+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							activeTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								activeTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								activeTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// activeTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							activeTemp+='<div class="zhonbao-des"></div>';
						}*/
							activeTemp+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						activeTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						activeTemp+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				activeTemp+='</div>';
			}
				activeTarget.empty().append(activeTemp);
				$(".loading-tips").hide();
				rowAddMin();
				kernel.hideLoading();
				
			}
		});
	}
	// getDataList('ZS201801191726514267');

	$(".actab-title-item").click(function(){
		var exhibitionId=$(this).attr("data-code");
		getDataList(exhibitionId);
	});
	var titTop = $(".actab-title").offset().top;
	var tabHeight = $(".actab-title-cont").height();
	$(".actab-title").css("height",tabHeight);
	$dom.scroll(function(){
		if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
		var sHtop = $dom.scrollTop();//滚动条距离顶部的距离
		if(sHtop>titTop){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时
			$(".actab-title-cont").addClass("fixed");
			
		}else if(sHtop<titTop){
			$(".actab-title-cont").removeClass("fixed");
		};
	
	});
	$(".getcoupons").click(function(ev){
		var dataid = $(this).attr("data-id");
		$dom.css("overflow-y","hidden");
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/voucher/receiveVoucher",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,voucherTemplateId:dataid},
			dataType: "json",
			success: function(data) {
				console.log(data)
				if (data.status=="success") {
					$(".redpopup1").show();
				}else{
					$(".redpopup2 .text").text(data.msg);
					$(".redpopup2").show();
				}
			}
		});
	})
	$(".confirm").click(function(){
		$(".redpopup").hide();
		$dom.css("overflow-y","auto");
	});
	backTop(thispage);
	
	return {
		onload: function(force) {
			
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
            $("#to_top").hide();
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});