'use strict';
define(['module', 'common/kernel/kernel', 'common/zepto/zepto'], function(module, kernel, zepto) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.daydeal");

	$('body').off('touchmove');

	
	function timeStamp( second_time ){ 
		var time = parseInt(second_time) + "秒";  
		if( parseInt(second_time )> 60){  
		  
		    var second = parseInt(second_time) % 60;  
		    var min = parseInt(second_time / 60);  
		    time = min + "分" + second + "秒";  
		      
		    if( min > 60 ){  
		        min = parseInt(second_time / 60) % 60;  
		        var hour = parseInt( parseInt(second_time / 60) /60 );  
		        time = hour + "时" + min + "分" + second + "秒";  
		  
		        if( hour > 24 ){  
		            hour = parseInt( parseInt(second_time / 60) /60 ) % 24;  
		            var day = parseInt( parseInt( parseInt(second_time / 60) /60 ) / 24 );  
		            time = day + "天" + hour + "时" + min + "分" + second + "秒";  
		        }  
		    }
	}  
	  
	return time;          
	} 
	
	
	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/fetchDailyDiscountData?dayFlag=1",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId,limit:100},
		dataType: "json",
		async:true,
		success: function(res) {
			if(res.status=="success"){
				if(res.data.rows == null){
					$(".daydeal-box1").hide();
					return;
				}
				var timesVal = res.data.rows[0].endTimeStamp/1000;
				setInterval(function(){
					if(timesVal<=0){
						$(".cutdown-time1").text("已结束");
						$(".daydeal-box1").hide();
					}else{
						timesVal--;
						$(".cutdown-time1").text(timeStamp(timesVal));
					}
				},1000);
				var daydealtemp1=" ";
				var daydealTarget1=$(".daydeal-list1");
				if(res.data.rows.length>0){
					for(var i=0; i<res.data.rows.length;i++){
						daydealtemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="daydeal-item">\
							<img class="photo" src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">\
							<div class="name text-overflow">'+res.data.rows[i].commonName+'</div>';
								if(res.data.rows[i].isControl==1){
									if(res.data.rows[i].isPurchase==true){
										if(res.data.rows[i].isControlPriceToMe==1){
											daydealtemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											if(res.data.rows[i].priceType==1){
												daydealtemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
											}else{
												daydealtemp1+='<div class="price text-overflow">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
											}
										}
										
										
									}else{
										daydealtemp1+='<div class="price" style="color:#eab41f;">暂无购买权限</div>';
									}
								}else{
									if(res.data.rows[i].isControlPriceToMe==1){
										daydealtemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										if(res.data.rows[i].priceType==1){
											daydealtemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											daydealtemp1+='<div class="price text-overflow">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
										}
									}
								}
								
								daydealtemp1+='<div class="norms text-overflow">'+res.data.rows[i].spec+'</div>';
							if(res.data.rows[i].status=="2"){
								daydealtemp1+='<div class="gone-icon"></div>';
							};
							if(res.data.rows[i].markerUrl){
								daydealtemp1+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
							};
						daydealtemp1+='</a>';
					}	
					daydealTarget1.empty().append(daydealtemp1);
				}
				
				
			}
		}
	});	

	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/fetchDailyDiscountData?dayFlag=2",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId,limit:100},
		dataType: "json",
		async:true,
		success: function(res) {
			if(res.status=="success"){
				if(res.data.rows == null){
					$(".daydeal-box2").hide();
					return;
				}
				var timesVal = res.data.rows[0].endTimeStamp/1000;
				setInterval(function(){
					if(timesVal<=0){
						$(".cutdown-time2").text("已结束");
						$(".daydeal-box2").hide();
					}else{
						timesVal--;
						$(".cutdown-time2").text(timeStamp(timesVal));
					}
				},1000);
				var daydealtemp2=" ";
				var daydealTarget2=$(".daydeal-list2");
				if(res.data.rows.length>0){
					for(var i=0; i<res.data.rows.length;i++){
						daydealtemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="daydeal-item">\
							<img class="photo" src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">\
							<div class="name text-overflow">'+res.data.rows[i].commonName+'</div>';
								if(res.data.rows[i].isControl==1){
									if(res.data.rows[i].isPurchase==true){
										if(res.data.rows[i].isControlPriceToMe==1){
											daydealtemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											if(res.data.rows[i].priceType==1){
												daydealtemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
											}else{
												daydealtemp2+='<div class="price text-overflow">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
											}
										}
									}else{
										daydealtemp2+='<div class="price" style="color:#eab41f;">暂无购买权限</div>';
									}
								}else{
									if(res.data.rows[i].isControlPriceToMe==1){
										daydealtemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										if(res.data.rows[i].priceType==1){
											daydealtemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											daydealtemp2+='<div class="price text-overflow">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
										}
									}
								}
								
								daydealtemp2+='<div class="norms text-overflow">'+res.data.rows[i].spec+'</div>';
							if(res.data.rows[i].status=="2"){
									daydealtemp2+='<div class="gone-icon"></div>';
								};
							if(res.data.rows[i].markerUrl){
								daydealtemp2+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
							};
						daydealtemp2+='</a>';
					}	
					daydealTarget2.empty().append(daydealtemp2);
				}
				
				
			}
		}
	});	

	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/fetchDailyDiscountData?dayFlag=3",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId,limit:100},
		dataType: "json",
		async:true,
		success: function(res) {
			if(res.status=="success"){
				if(res.data.rows == null){
					$(".daydeal-box3").hide();
					return;
				}
				var timesVal = res.data.rows[0].endTimeStamp/1000;
				setInterval(function(){
					if(timesVal<=0){
						$(".cutdown-time3").text("已结束");
						$(".daydeal-box3").hide();
					}else{
						timesVal--;
						$(".cutdown-time3").text(timeStamp(timesVal));
					}
				},1000);
				var daydealtemp3=" ";
				var daydealTarget3=$(".daydeal-list3");
				if(res.data.rows.length>0){
					for(var i=0; i<res.data.rows.length;i++){
						daydealtemp3+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="daydeal-item">\
							<img class="photo" src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">\
							<div class="name text-overflow">'+res.data.rows[i].commonName+'</div>';
								if(res.data.rows[i].isControl==1){
									if(res.data.rows[i].isPurchase==true){
										if(res.data.rows[i].isControlPriceToMe==1){
											daydealtemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											if(res.data.rows[i].priceType==1){
												daydealtemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
											}else{
												daydealtemp3+='<div class="price text-overflow">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
											}
										}
									}else{
										daydealtemp3+='<div class="price" style="color:#eab41f;">暂无购买权限</div>';
									}
								}else{
									if(res.data.rows[i].isControlPriceToMe==1){
										daydealtemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										if(res.data.rows[i].priceType==1){
											daydealtemp3+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											daydealtemp3+='<div class="price text-overflow">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
										}
									}
								}
								
								daydealtemp3+='<div class="norms text-overflow">'+res.data.rows[i].spec+'</div>';
							if(res.data.rows[i].status=="2"){
									daydealtemp3+='<div class="gone-icon"></div>';
								}
							if(res.data.rows[i].markerUrl){
								daydealtemp3+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
							};
						daydealtemp3+='</a>';
					}	
					daydealTarget3.empty().append(daydealtemp3);
				}
				
				
			}
		}
	});	
	
	return {
		onload: function(force) {
		
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});
