'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
		var $dom = $("#page>.content>.pinpailist");

		//添加点击关闭所有tabs的事件
		$(".top-tab-cont-popup").click(function(){
			$(".top-tab-cont-popup").hide();
			$dom.css("overflow-y","auto");
		});

		var tabdata = [];
		var scrollStar=true;
		var limitPage=10;
		var curPage=1;
		var totalPage;

		$dom.scroll(function(){
			var bheight = $(document).height();//获取窗口高度
			var sheight = kernel.getScrollHeight(dom);//获取滚动条高度，[0]是为了把jq对象转化为js对象
			var stop = $dom.scrollTop();//滚动条距离顶部的距离
			if(totalPage == 1){
				return;
			}else{
				if(stop>=sheight-bheight && stop>1 && scrollStar){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时
					scrollStar=false;
					if(curPage >= totalPage){
						if(totalPage!=0){
							$(".loading-tips").css("display","none");
							$(".pinpaibox").append("<p style='text-align:center;padding:10px 0;color:#999'>无更多数据</p>")
						}
					}else{
						getdatalist(curPage)
						curPage++;
					}	
				}
			}
			
		});

		

	$.ajax({
		type: "POST",
		url: apiUrl+"/app/activity/preferredBrand?sort=1&limit=1000",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		async:true,
		success: function(res) {
			if(res.status=="success"){
				totalPage = res.data.preferredBrandList.pageCount;
				tabdata = res.data.preferredBrandList.rows;
				//展示初次页面显示的十条数据；
				getdatalist(0);

				//只有一条数据，显示无更多数据
				if(totalPage == 1){
					$(".loading-tips").css("display","none");
					$(".pinpaibox").append("<p style='text-align:center;padding:10px 0;color:#999'>无更多数据</p>")
				}
				//循环生成tab图标
				var toptabtemp = '';
				var toptabtarget = $('.top-tab');
				if(tabdata.length <= 6){
					for(var i in tabdata){
						toptabtemp += '<a href="'+tabdata[i].appUrl+'" class="dapai-link"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].logoImg+'"></a>'
					}
					toptabtarget.empty().append(toptabtemp);
					return;
				}else{
					for(var i in tabdata.slice(0,5)){
						toptabtemp += '<a href="'+tabdata[i].appUrl+'" class="dapai-link"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].logoImg+'"></a>'
					}
					toptabtemp += '<a href="javascript:;" class="tab-open-btn"><img src="/static/public/pinpailist/all.jpg"></a>'
				}
				
				toptabtarget.empty().append(toptabtemp);
				//添加点击显示全部的事件
				$(".tab-open-btn").click(function(){
					$(".top-tab-cont-popup").show();
					$dom.css("overflow-y","hidden");
				});

				//循环生成全部的tab图标
				var toptabconttemp = '';
				var toptabconttarget = $('.top-tab-cont');
				for(var i=0; i<5; i++){
					if(i == 0){
						toptabconttemp +='<div><a href="'+tabdata[i].appUrl+'"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].logoImg+'"><div class="tab-title">'+tabdata[i].brandName+'</div></a>'
					}else if(i == 4){
						toptabconttemp += '<a href="'+tabdata[i].appUrl+'"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].logoImg+'"><div class="tab-title">'+tabdata[i].brandName+'</div></a><a href="javascript:;" class="tab-close-btn"><img src="/static/public/pinpailist/close.jpg"><div class="tab-title"></div></a></div>'
					}else{
						toptabconttemp += '<a href="'+tabdata[i].appUrl+'"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].logoImg+'"><div class="tab-title">'+tabdata[i].brandName+'</div></a>'
					}
				}
				for(var i=5; i<tabdata.length; i++){
					if((i+1)%6 == 0){
						toptabconttemp +='<div><a href="'+tabdata[i].appUrl+'"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].logoImg+'"><div class="tab-title">'+tabdata[i].brandName+'</div></a>'
					}else if((i+2)%6 == 0 || i == tabdata.length-1){
						toptabconttemp += '<a href="'+tabdata[i].appUrl+'"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].logoImg+'"><div class="tab-title">'+tabdata[i].brandName+'</div></a></div>'
					}else{
						toptabconttemp += '<a href="'+tabdata[i].appUrl+'"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].logoImg+'"><div class="tab-title">'+tabdata[i].brandName+'</div></a>'
					}	
				}
				
				toptabconttarget.empty().append(toptabconttemp);
			}
		}
	});	

	//获取展示的商品;
	function getdatalist(cur){
		if(cur < totalPage - 1){
			var num = (cur + 1)*10
		}
		if(cur == totalPage -1){
			var num = tabdata.length;
		}
		for(var i =cur*10; i < num ; i++){
			var classname = "pinpai-list-box" + i;
			$('.pinpai-list').append('<div class="'+classname+' pin-box"></div>');
			var domname = ".pinpai-list-box" + i;
			$(domname).append('<a class="pinpai-link" href="'+tabdata[i].appUrl+'"><img src="'+hostUrl+'/ybm/brand/'+tabdata[i].backgroundImg+'"></a>');
			$(domname).css("background-color",tabdata[i].backgroundColor);
			(function(m){
				$.ajax({
					type: "POST",
					url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId="+tabdata[i].typeCode+"&sort=1&limit=4",
					headers : {'version':version,'terminalType':terminalType},
					data: {"merchantId":merchantId},
					dataType: "json",
					async:true,
					success: function(res) {
						if(res.status=="success"){
							if(res.data.rows.length>0){
								var listtemp = '<div class="pinpai-list-box"><div class="pinpai-list-cont">';
								for(var i=0; i<res.data.rows.length;i++){
									listtemp +='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="pinpailist-item">\
										<img class="photo" src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">\
										<div class="name text-overflow">'+res.data.rows[i].commonName+'</div>';
											if(res.data.rows[i].isControl==1){
												if(res.data.rows[i].isPurchase==true){
													if(res.data.rows[i].isControlPriceToMe==1){
														listtemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
													}else{
														if(res.data.rows[i].priceType==1){
															listtemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
														}else{
															listtemp+='<div class="price text-overflow">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
														}
													}
												}else{
													listtemp+='<div class="price" style="color:#eab41f;">暂无购买权限</div>';
												}
											}else{
												if(res.data.rows[i].isControlPriceToMe==1){
													listtemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
												}else{
													if(res.data.rows[i].priceType==1){
														listtemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
													}else{
														listtemp+='<div class="price text-overflow">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
													}
												}
											}
											
											listtemp+='<div class="norms text-overflow">'+res.data.rows[i].spec+'</div>';
										if(res.data.rows[i].status=="2"){
											listtemp+='<div class="gone-icon"></div>';
										};
										if(res.data.rows[i].markerUrl){
											listtemp+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
										};
									listtemp+='</a>';
								}
								listtemp += '<a href="'+tabdata[m].appUrl+'" class="pinpailist-item seeall"></a></div></div>';
								var boxdom = ".pinpai-list-box" + m;
								$(boxdom).append(listtemp);

							}	
						}
					}
				});		
			})(i)				
		}
		scrollStar=true;
	}
	
	
	
   backTop(thispage);
	
	
	
	return {
		onload: function(force) {
			setAppTitle("品牌推荐");
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});