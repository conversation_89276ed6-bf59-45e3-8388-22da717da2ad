#page > .content > .gaomaonew {
  border-top: none;
  background: #f8f8f8;
  overflow-y: auto;
}
#page > .content > .gaomaonew .chu-title {
  display: block;
}
#page > .content > .gaomaonew .baktitlebox {
  height: 42px;
  display: none;
}
#page > .content > .gaomaonew .tab-section {
  width: 100%;
  background: #fff;
  position: relative;
  overflow: visible;
  top: 0;
  z-index: 100;
}
#page > .content > .gaomaonew .tab-section .tab-title-arrow {
  display: block;
  width: 45px;
  height: 45px;
  background: #fff url(images/icon-up.png) no-repeat 13px 18px;
  background-size: 35%;
  position: absolute;
  top: 0;
  right: 0;
}
#page > .content > .gaomaonew .tab-section .tab-title-arrow.open {
  background: #fff url(images/icon-down.jpg) no-repeat 13px 18px;
  background-size: 35%;
}
#page > .content > .gaomaonew .tab-section .tab-title-section {
  height: 47px;
  width: 100%;
  background: #fff;
  display: none;
}
#page > .content > .gaomaonew .tab-section .tab-title-section .tab-title-section-text {
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  line-height: 45px;
}
#page > .content > .gaomaonew .tab-section .tab-more-section {
  background: rgba(255, 255, 255, 0.9);
  position: absolute;
  width: 100%;
  z-index: 400;
  display: none;
}
#page > .content > .gaomaonew .tab-section .tab-more-section .tab-more-item {
  display: inline-block;
  width: 25%;
  text-align: center;
  height: 40px;
  line-height: 40px;
  float: left;
  color: #333;
  position: relative;
}
#page > .content > .gaomaonew .tab-section .tab-more-section .tab-more-item.cur {
  color: #00c775;
}
#page > .content > .gaomaonew .tab-section .tab-more-section .tab-more-item.cur:after {
  content: '';
  display: inline-block;
  position: absolute;
  width: 80%;
  left: 50%;
  margin-left: -40%;
  bottom: 0;
  border-bottom: 2px solid #00c775;
  z-index: 2;
}
#page > .content > .gaomaonew .tab-section .tab-title {
  position: relative;
  width: 100%;
  height: 45px;
  background: #fff;
}
#page > .content > .gaomaonew .tab-section .tab-title .scrolltitle {
  position: absolute;
  height: 45px;
}
#page > .content > .gaomaonew .tab-section .tab-title .scrolltitle ul {
  padding: 0;
  margin: 0;
  width: 750px;
  height: 45px;
  white-space: nowrap;
  overflow: hidden;
  background-color: #fff;
}
#page > .content > .gaomaonew .tab-section .tab-title .scrolltitle .tab-title-item {
  display: inline-block;
  height: 45px;
  padding: 0 7px;
  float: left;
  line-height: 45px;
  font-size: 16px;
  color: #333;
  position: relative;
}
#page > .content > .gaomaonew .tab-section .tab-title .scrolltitle .tab-title-item.cur {
  color: #00c775;
}
#page > .content > .gaomaonew .tab-section .tab-title .scrolltitle .tab-title-item.cur:after {
  content: '';
  display: inline-block;
  position: absolute;
  width: 80%;
  left: 50%;
  margin-left: -40%;
  bottom: 0;
  border-bottom: 2px solid #00c775;
  z-index: 2;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont {
  margin: 0 0.5%;
  min-height: 550px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item {
  margin: 0.5%;
  width: 49%;
  background: #fff;
  border-radius: 4px;
  float: left;
  position: relative;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo {
  display: block;
  padding: 10px;
  position: relative;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo img {
  height: 140px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box {
  padding: 0 5px;
  position: absolute;
  left: 0;
  bottom: 0;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box span {
  font-size: 12px;
  padding: 2px 2px;
  line-height: 12px;
  color: #fff;
  border-radius: 4px 0 0 4px;
  background: #2b343f;
  display: inline-block;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box i {
  font-size: 12px;
  display: inline-block;
  background: #fff;
  color: #666;
  border: 1px solid #2b343f;
  padding: 0 2px;
  border-radius: 0 4px 4px 0;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box .control-price {
  float: left;
  display: flex;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box .control-price i {
  flex: 1;
  line-height: 14px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box .control-gross {
  float: left;
  display: flex;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box .control-gross i {
  flex: 1;
  line-height: 14px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box .control-gross span {
  margin-left: 3px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .commonName {
  height: 24px;
  display: block;
  line-height: 24px;
  padding-left: 5px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .commonName .dujia {
  float: left;
  color: #fff;
  padding: 1px 2px;
  font-size: 12px;
  line-height: 12px;
  border-radius: 3px;
  font-weight: 400;
  background: #27adff;
  margin-top: 5px;
  margin-right: 3px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .commonName .name {
  color: #333;
  font-size: 14px;
  line-height: 24px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .price {
  color: #ff2400;
  padding: 5px;
  font-weight: bold;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .norms {
  margin: 0 5px 3px;
  font-size: 12px;
  line-height: 14px;
  height: 14px;
  color: #999;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .norms .zhonbao-des {
  margin-left: 5px;
  display: inline;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .biaoqian {
  height: 20px;
  display: flex;
  flex-direction: rows;
  align-items: center;
  padding-left: 5px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .biaoqian div {
  padding: 1px 3px;
  margin-right: 5px;
  line-height: 12px;
  font-size: 12px;
  color: #fff;
  border-radius: 3px;
  display: inline-block;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .biaoqian div.quan {
  background: #ff0000;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .biaoqian div.linqi {
  background: #ff9400;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .biaoqian div.normal {
  color: #ff0e0e;
  padding: 0px 2px;
  border: 1px solid #ff9595;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .biaoqian div.fubiao {
  background: #e4e4e4;
  color: #333;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .handle {
  width: 94%;
  margin: 0px auto 8px;
  height: 27px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .handle .inp-total {
  display: inline-block;
  width: 35px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  opacity: 1;
  color: #333;
  border: none;
  font-size: 12px;
  background: #efefef;
  float: right;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .handle .min {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 15px 0 0 15px;
  color: #666;
  float: right;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .handle .add {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 0 15px 15px 0;
  color: #666;
  float: right;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .handle-lon {
  width: 94%;
  margin: 0 auto 8px;
  height: 27px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .handle-lon .add-lon {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 50%;
  color: #666;
  float: right;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .gone-icon {
  display: block;
  position: absolute;
  width: 100%;
  height: 65%;
  background: url(images/<EMAIL>) center center no-repeat;
  background-size: 30%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .markerUrl {
  position: absolute;
  width: 40px;
  height: 48px;
  top: -2px;
  left: -1px;
}
#page > .content > .gaomaonew .chu-box-da .gaomaonew-btm .gaomaonew-btn {
  margin: 30px auto;
  font-size: 12px;
  width: 170px;
  text-align: center;
  line-height: normal;
  padding: 8px 0;
  color: #fff9e6;
  background: #00c775;
  border-radius: 13px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list {
  min-height: 250px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item {
  padding: 5px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .photo {
  display: inline-block;
  width: 90px;
  height: 90px;
  float: left;
  background: #fff;
  padding: 5px;
  border-radius: 4px;
  position: relative;
  margin-top: 10px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .photo img {
  display: block;
  width: 100%;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .photo .nofanmark {
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: rgba(105, 112, 119, 0.7);
  color: #fff;
  text-align: center;
  line-height: 16px;
  height: 16px;
  font-size: 12px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info {
  width: 67%;
  float: right;
  position: relative;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .a-link-mask {
  display: block;
  width: 50%;
  height: 90px;
  position: absolute;
  top: 0;
  left: 0;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-title {
  height: 17px;
  width: 100%;
  margin-top: 3px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-title .pro-title {
  width: 100%;
  color: #333;
  height: 16px;
  float: left;
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-title .pro-title .dujia {
  display: inline-block;
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
  line-height: 14px;
  border-radius: 3px;
  font-weight: 400;
  background: #27adff;
  vertical-align: top;
  margin-right: 3px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-title .coll-btn {
  display: inline-block;
  float: right;
  width: 25px;
  height: 25px;
  background: url('images/<EMAIL>') no-repeat;
  background-size: 100%;
  margin-top: -3px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-title .coll-btn.colled {
  background: url('images/<EMAIL>') no-repeat;
  background-size: 100%;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-nomal {
  color: #999;
  padding: 5px 0;
  font-size: 13px;
  height: 22px;
  overflow: hidden;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-nomal .zhonbao-des {
  display: inline;
  padding-left: 8px;
  height: 16px;
  margin-top: 10px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .biaoqian {
  height: 20px;
  display: flex;
  flex-direction: rows;
  align-items: center;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .biaoqian div {
  padding: 1px 3px;
  margin-right: 5px;
  line-height: 12px;
  font-size: 12px;
  color: #fff;
  border-radius: 3px;
  display: inline-block;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .biaoqian div.quan {
  background: #ff0000;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .biaoqian div.linqi {
  background: #ff9400;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .biaoqian div.normal {
  color: #ff0e0e;
  padding: 0 2px;
  border: 1px solid #ff9595;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .biaoqian div.fubiao {
  background: #e4e4e4;
  color: #333;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-profits {
  margin-top: 6px;
  height: 21px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-profits span {
  float: left;
  text-align: center;
  font-size: 12px;
  line-height: 18px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-profits span.span3 {
  margin-left: 10px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-profits span.span1,
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-profits span.span3 {
  color: #fff;
  padding: 0 6px;
  background: #2b343f;
  width: 48px;
  border-radius: 4px 0 0 4px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-profits span.span2,
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-profits span.span4 {
  border: 1px solid #989da2;
  border-left: 0;
  line-height: 16px;
  padding: 0 4px;
  border-radius: 0 4px 4px 0;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot {
  height: 27px;
  margin-top: 5px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .cut-time {
  font-size: 12px;
  float: left;
  width: 40%;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .bot-price {
  display: inline-block;
  width: 59%;
  float: left;
  font-weight: 600;
  font-size: 16px;
  height: 27px;
  line-height: 27px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .purch {
  font-size: 12px;
  display: inline-block;
  border: 1px solid #fc0300;
  text-align: center;
  border-radius: 4px;
  float: left;
  margin-top: 6px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .handle {
  width: 41%;
  float: right;
  height: 27px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .handle .inp-total {
  display: inline-block;
  width: 35px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  opacity: 1;
  color: #333;
  border: none;
  font-size: 12px;
  background: #efefef;
  float: right;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .handle .min {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 15px 0 0 15px;
  color: #666;
  float: right;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .handle .add {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 0 15px 15px 0;
  color: #666;
  float: right;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .handle-lon {
  width: 55%;
  float: right;
  height: 27px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .handle-lon .add-lon {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 22px;
  font-size: 24px;
  border: 1px solid #ccc;
  border-radius: 50%;
  color: #666;
  float: right;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .pro-info .pro-info-bot .lackbuy {
  color: #da9f3b;
  line-height: 30px;
  text-align: right;
  padding-right: 3px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .gone-icon {
  position: absolute;
  width: 13%;
  height: 87%;
  background: url(images/<EMAIL>) center center no-repeat;
  background-size: 100%;
  left: 24px;
}
#page > .content > .gaomaonew .chu-box-xiao .gaomaonew-list .gaomaonew-item .markerUrl {
  position: absolute;
  width: 30px;
  height: 35px;
  top: 0;
  left: 0;
}
@media (max-width: 480px) {
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo img {
    height: 166px;
  }
}
@media (max-width: 414px) {
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo img {
    height: 159px;
  }
}
@media (max-width: 412px) {
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo img {
    height: 158px;
  }
}
@media (max-width: 375px) {
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo img {
    height: 162px;
  }
}
@media (max-width: 350px) {
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo img {
    height: 155px;
  }
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box {
    height: 40px;
  }
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box span {
    width: 54px;
    text-align: center;
  }
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box i {
    text-align: center;
    padding: 0 8px;
  }
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box .control-gross span {
    margin-left: 0;
  }
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo .control-box .control-gross {
    margin-top: 3px;
  }
}
@media (max-width: 320px) {
  #page > .content > .gaomaonew .chu-box-da .gaomaonew-list .gaomaonew-cont .gaomaonew-item .photo img {
    height: 135px;
  }
}
