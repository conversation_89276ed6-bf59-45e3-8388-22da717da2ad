'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel,jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.huarunnew");
	$('body').off('touchmove');
	var scrollStar=true;
	var limitPage=10;
	// var curPage=1;
	var totalPage;
		$dom.scroll(function(){
			if(browser.name==="IOS"){
				try{
					window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
				}catch(err){

				}
			}
			/*var bheight = $(document).height();//获取窗口高度
			var sheight = kernel.getScrollHeight(dom);//获取滚动条高度，[0]是为了把jq对象转化为js对象
			var stop = $dom.scrollTop();//滚动条距离顶部的距离
			if(stop>=sheight-bheight && stop>1 && scrollStar){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时
				
				scrollStar=false;
					if(curPage>=totalPage){
						if(totalPage!=0){
							$(".loading-tips").css("display","none");
							$(".huarunnew-list").append("<p style='text-align:center;padding:10px 0 25px 0;color:#fff'>无更多数据</p>")
						}
					}else{
						getDataList(curPage);
						curPage++;
						
					}
				
			}*/
		});

	function getDataList(code,dom){
		
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?sort=1",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,limit:1000,offset:0,exhibitionId:code},
			dataType: "json",
			success: function(res) {
				var huarunnewTemp = " ";
				var huarunnewTarget = $(dom);
				totalPage = res.data.pageCount;
				
				for(var i=0;i<res.data.rows.length;i++){
				huarunnewTemp+='<div class="huarunnew-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					huarunnewTemp += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							huarunnewTemp += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							huarunnewTemp += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							huarunnewTemp += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					huarunnewTemp+='</div></a>'
				}
				huarunnewTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							huarunnewTemp+='<div class="dujia">独家</div>'
						}
					huarunnewTemp+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';		
				huarunnewTemp+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				huarunnewTemp+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:huarunnewTemp+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:huarunnewTemp+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:huarunnewTemp+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:huarunnewTemp+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				huarunnewTemp+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									huarunnewTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										huarunnewTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										huarunnewTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// huarunnewTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										huarunnewTemp+='<div class="zhonbao-des"></div>';
									}*/
										huarunnewTemp+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								huarunnewTemp+='<div class="price">暂无购买权限</div>';
								huarunnewTemp += '<div class="control-box"></div>';
								// huarunnewTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								huarunnewTemp+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							huarunnewTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								huarunnewTemp+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								huarunnewTemp+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// huarunnewTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							huarunnewTemp+='<div class="zhonbao-des"></div>';
						}*/
							huarunnewTemp+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						huarunnewTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						huarunnewTemp+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				huarunnewTemp+='</div>';
			}
				huarunnewTarget.append(huarunnewTemp);
				$('.loading-tips').hide()
				scrollStar=true;
				rowAddMin();

			}
		});
	}

	getDataList('ZS201803281143201510','.huarunnew-cont1');


	$(".view-more-detail").click(function(){
		$(".detail-box").show();
		$dom.css("overflow-y","hidden");
	})


	$(".detail-box").click(function(){
		$(".detail-box").hide();
		$dom.css("overflow-y","auto");
	})
	$('.detail-msg').click(function(e){
		e.stopPropagation() || event.preventDefault();
	})

	backTop(thispage);
	
	return {
		onload: function(force) {
		
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});