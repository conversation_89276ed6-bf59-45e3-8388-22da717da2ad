#page>.content>.pharmacyvideo{
	border-top: none;
	background: #f8f8f8;
	overflow-y: auto;
	.pharmacyvideo-content{
		.zixun-content{
			padding:5px 10px 20px;
			.title{
				font-size:24px;
				line-height:32px;
				color:#333;
			}
			.time-box{
				font-size:12px;
				color:#999;
				padding-top:10px;
				.time{
					float:left;
				}
				.watch-time{
					float:right;
				}
			}
		}
		.video-box{
			// width:94%;
			// margin:10px auto 0;
			video{
				width:100%;
				// height:230px;
			}
			.model-box{
				display:inline-block;
				position:absolute;
				z-index:1;
				left:0;
				top:0;
				width:100%;
				height:230px;
				border-radius:4px;
				background:url('./images/play.png') center center no-repeat;	
				background-size:18%;
				background-color:rgba(250,250,250,0.5);
				display: none;
			}
		}
		.video-mesg{
			padding-left:10px;
			background:#fff;
			.title{
				font-size:20px;
				line-height:28px;
				font-weight:700;
				color:#333;
				margin-top:8px;
			}
			.time-box{
				font-size:12px;
				line-height:16px;
				color:#999;
				margin:6px 0 2px;
				.publictime{
					float:left;
				}
				.watchtimes{
					margin-left: 16px;
					float:left;
				}
			}
		}
		.content{
			margin-top:10px;
			padding:12px 10px 0 10px;
			background:#fff; 
			.title{
				font-size:16px;
				line-height:22px;
				font-weight:700;
				.token{
					display:inline-block;
					width:3px;
					height:14px;
					border-radius:1px;
					background-image: linear-gradient(0deg, #000000 0%, #878787 100%); 
					margin-right:5px;
					vertical-align:middle;
				}
			}
			.detail{
				font-size:14px;
				line-height:18px;
				color:#666;
			}
		}
	}
}
@media(max-width:480px) {
	#page > .content > .pharmacyvideo .chu-box-da .pharmacyvideo-cont .pharmacyvideo-item .photo img {
	
	}
}
