'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery', 'common/IScroll/IScroll'], function(module, kernel, jquery, IScroll) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.ninediscount");	
	var myscroll=new IScroll(".tab-title",{scrollX: true, scrollbars: false, click: true });
	var scrollTop1 = $('.ninediscount .tab-section').offset().top;
	var itemIndex = 0 ;
	var typeCodeArr=['ZS201803071130188620','ZS201803071130392824','ZS201803071134004651','ZS201803071134324748','ZS201803071134561822','ZS201803071135182624'];
	var chooseCode = typeCodeArr[0];
	// 打开页面，跳转到制定的tab栏
	var tabIndex = kernel.location.args.tabindex;
	if(tabIndex){
		itemIndex = tabIndex - 1;
		chooseCode = typeCodeArr[itemIndex];
		changeClass();
		tabscroll();
	}
	$(".tab-title-arrow").click(function(){
		$(".tab-title").toggle();
		$(".tab-title-section").toggle();
		$(".tab-more-section").toggle();
		// $(this).toggleClass("open");
		
	});
	// $('.ninediscount .tab-section').click(function(){
		
	// 	$dom.animate({  
 //            scrollTop: scrollTop1
 //        }, 0);
 //        $('.ninediscount .tab-section').css("position","fixed");
	// })

   $dom.scroll(function(){
   		if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
		
		var sHtop = $(dom).scrollTop();
		if(sHtop >= scrollTop1){
			$('.ninediscount .tab-section').css("position","fixed");
			$(".baktitlebox").show();
		}else{
			$('.ninediscount .tab-section').css("position","relative");
			$('.ninediscount .tab-title').css("display",'block');
			// $('.ninediscount .tab-title-arrow').removeClass("open")
			$(".baktitlebox").hide();
		}
		$('.ninediscount .tab-more-section').css('display',"none");
		$('.ninediscount .tab-title').css('display',"block");
	});


	function getdatalist(){
		$('.load-tip').css('display','block')
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId="+chooseCode+"&sort=1&limit=1000",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,offset:0},
			dataType: "json",
			success: function(res) {
				var ninediscountTemp2 = " ";
				var ninediscountTarget2 = $(".ninediscount-cont2");

				if(res.isDig==1){
					$(".wajinbibtn").show();
					$(".wajinbibtn").attr("data-id",res.digId);
					getjinbi(thispage);
				}else{
					$(".wajinbibtn").hide();
				}

				$('.load-tip').css('display','none')
				for(var i=0;i<res.data.rows.length;i++){
					ninediscountTemp2+='<div class="ninediscount-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
					if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
						ninediscountTemp2 += '<div class="control-box">';
							if(res.data.rows[i].uniformPrice){
								ninediscountTemp2 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
							};
							if(res.data.rows[i].suggestPrice){
								ninediscountTemp2 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
							};
							if(res.data.rows[i].grossMargin){
								ninediscountTemp2 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
							};
						ninediscountTemp2+='</div></a>'
					}
					ninediscountTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							ninediscountTemp2+='<div class="dujia">独家</div>'
						}
					ninediscountTemp2+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
					ninediscountTemp2+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
					ninediscountTemp2+='<div class="biaoqian">';
					if(res.data.rows[i].tagList){
						for(var j in res.data.rows[i].tagList.slice(0,3)){
							switch(res.data.rows[i].tagList[j].uiType){
								case 1:ninediscountTemp2+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
								case 2:ninediscountTemp2+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
								case 3:ninediscountTemp2+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
								case 4:ninediscountTemp2+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
								default:;break;
							}
						}
					}
					ninediscountTemp2+='</div>'
						if(res.data.rows[i].isControl==1){
								if(res.data.rows[i].isPurchase==true){
									if(res.data.rows[i].isControlPriceToMe==1){
										ninediscountTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										if(res.data.rows[i].priceType==1){
											ninediscountTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
										}else{
											ninediscountTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
										}
									}
									
										// if (res.data.rows[i].isSplit==0) {
											// ninediscountTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
										/*}else{
											ninediscountTemp2+='<div class="zhonbao-des"></div>';
										}*/
											ninediscountTemp2+='<div class="handle">\
													<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
													<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
													<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
												</div>';
										
								}else{
									ninediscountTemp2+='<div class="price">暂无购买权限</div>';
									ninediscountTemp2 += '<div class="control-box"></div>';
									// ninediscountTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
									ninediscountTemp2+='<div class="handle-lon"></div>';
								}
						}else{
							if(res.data.rows[i].isControlPriceToMe==1){
								ninediscountTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								if(res.data.rows[i].priceType==1){
									ninediscountTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									ninediscountTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
								}
							}
				
							// if (res.data.rows[i].isSplit==0) {
								// ninediscountTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
							/*}else{
								ninediscountTemp2+='<div class="zhonbao-des"></div>';
							}*/
								ninediscountTemp2+='<div class="handle">\
										<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
										<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
										<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
									</div>';
							
						}
						
					
						if(res.data.rows[i].status=="2"){
							ninediscountTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
						};
						if(res.data.rows[i].markerUrl){
							ninediscountTemp2+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
						}
						
					ninediscountTemp2+='</div>';
				}


				ninediscountTarget2.append(ninediscountTemp2);
				
				rowAddMin();

				kernel.hideLoading();
				
				changebtnHtml();

				$('.ninediscount .ninediscount-btn').unbind();
				$('.ninediscount .ninediscount-btn').click(function(){
					$(".ninediscount-cont2").empty();
					kernel.showLoading();
					getdatalist();
					tabscroll();
					changeClass();
					$dom.animate({  
			            scrollTop: scrollTop1
			        }, 0);
			        $('.ninediscount .tab-section').css("position","fixed");
			        $(".baktitlebox").show();
				})
			}
		});
	}
	
	function tabscroll(){
		switch(itemIndex){
			case 0:myscroll.scrollTo(0, 0, 400);break;
			case 1:myscroll.scrollTo(0, 0, 400);break;
			case 2:myscroll.scrollTo(-80, 0, 400);break;
			case 3:myscroll.scrollTo(-120, 0, 400);break;
			case 4:myscroll.scrollTo(-180, 0, 400);break;
			case 5:myscroll.scrollTo(-210, 0, 400);break;
			default:;break;
		}
	}

	function changebtnHtml(){
		switch(itemIndex){
			case 0:$('.ninediscount-btn').html('点击跳转至“消化系统”');chooseCode="ZS201803071130392824";itemIndex = 1;break;
			case 1:$('.ninediscount-btn').html('点击跳转至“心脑血管”');chooseCode="ZS201803071134004651";itemIndex = 2;break;
			case 2:$('.ninediscount-btn').html('点击跳转至“抗生素类”');chooseCode="ZS201803071134324748";itemIndex = 3;break;
			case 3:$('.ninediscount-btn').html('点击跳转至“外用类”');chooseCode="ZS201803071134561822";itemIndex = 4;break;
			case 4:$('.ninediscount-btn').html('点击跳转至“其他类”');chooseCode="ZS201803071135182624";itemIndex = 5;break;
			case 5:$('.ninediscount-btn').html('点击跳转至“呼吸系统”');chooseCode="ZS201803071130188620";itemIndex = 6;break;
			default:break;
		}
	}

	function changeClass(){
		$('.tab-title-item').eq(itemIndex).addClass('cur').siblings().removeClass('cur');
		$('.tab-more-item').eq(itemIndex).addClass('cur').siblings().removeClass('cur');
	}
	$(".tab-more-item").click(function(){
		$(".tab-title").show();
		$(".tab-title-section").hide();
		$(".tab-more-section").hide();
		$(".tab-title-arrow").removeClass("open");
		itemIndex = $(this).index();
		chooseCode = typeCodeArr[itemIndex];
		$(".ninediscount-cont2").empty();
		getdatalist();
		kernel.showLoading();
		tabscroll();
		changeClass();
	});

	$('.tab-title-item').click(function(){
		itemIndex = $(this).index();
		chooseCode = typeCodeArr[itemIndex];
		$(".ninediscount-cont2").empty();
		getdatalist();
		kernel.showLoading();
		tabscroll();
		changeClass();

		$dom.animate({  
            scrollTop: scrollTop1
        }, 0);
        $('.ninediscount .tab-section').css("position","fixed");
        $(".baktitlebox").show();
	})

	getdatalist()
	
	return {
		onload: function(force) {
			// setAppTitle("九折专区");
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});