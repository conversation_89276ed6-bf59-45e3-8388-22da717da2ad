#page>.content>.feedback{
	overflow-y:auto;
    border-top: none;
    padding-top: 0;
    background: #f3f3f3;
    .tipe-text{
        height: 50px;
        line-height: 50px;
        text-align: center;
        color: #333;
    }
    .fed-inp5{
        margin-bottom: 40px;
        margin-top: 20px;
    }
	.fed-form{
        .type-item{
            display: inline-block;
            width: 27%;
            float: left;
            margin: 10px 3%;
            border:1px solid #dcdadd;
            border-radius: 3px;
            color: #a09da0;
            height: 35px;
            line-height: 35px;
            text-align: center;
            &.type-item-cur{
                background: url("./images/go.png") no-repeat top right;
                background-size: 17%;
                border:1px solid #59c37c;
                color: #59c37c;
            }
        }
        .fed-inp1{
            background: #fff;
            padding: 0 5%;
        }
        .fed-inp2{
            background: #fff;
            padding: 0 5%;
            margin-top: 10px;
        }
        .fedinp-bot{
            background: #fff;
            padding: 0 5%;
            margin-top: 10px;
        }
		.fed-inp{
		
			&.fed-inp-file{
				padding-top: 8px;
				margin-bottom: 20px;
				.inp-box{
					overflow: visible;
					.uploader-photo{
						overflow: visible;
						.photo-item{
							overflow: visible;
						}
					}
				}
			}
			.tit-inp{
				line-height: 30px;
                height: 30px;
                margin-bottom: 8px;
                color: #333;
                margin-top: 5px;
                display: block;
			}
			.inp-box{
				
			}
			.inp-sel{
				height: 30px;
				line-height: 30px;
				color: #666;
				background: #fff;
				width: 100%;
				border: 1px solid #dedede;
				border-radius: 3px;
			}
			.textarea-box{
				background: #f4f1f4;
				border: 1px solid #dedede;
				border-radius: 3px;
				.textarea {
				    display: block;
				    border: 0;
				    resize: none;
				    width: 100%;
				    color: inherit;
				    font-size: 1em;
				    line-height: inherit;
				    outline: 0;
				    color: #666;
                    background: #f4f1f4;
				}
				.textarea-counter {
				    color: #B2B2B2;
				    text-align: right;
				    padding: 0 5px 5px 0;
				}

			}
			.uploader-inp-box{
				display: inline-block;
				float: left;
				width: 77px;
				height: 77px;
				background: url(./images/addbtn.png) no-repeat;
				background-size: 100%;
				.imageform{
					width: 100%;
					height: 100%;
				}
				.uploader-input{
					display: block;
					width: 100%;
					height: 100%;
					opacity: 0;
				}
			}
			.uploader-photo{
				display: none;
				float: left;
				width: 77px;
				height: 77px;
				margin: 0 20px 0 0;
				.photo-item{
					position: relative;
					display: block;
					width: 77px;
					height: 77px;
					background: url(./images/default.png) no-repeat;
					background-size: 100%;
					.photo-item-img{
						position: absolute;
						width: 77px;
						height: 77px;
						img{
							display: block;
							width: 77px;
							height: 77px;
						}
					}
					.delbtn{
						display: none;
						width: 20px;
						height: 20px;
						position: absolute;
						top: -8px;
						right: -8px;
						background: url(./images/delbtn.png) no-repeat;
						background-size: 100%;
						z-index: 9;
					}
				}
			}
			.code-inp,.photo-inp{
				height: 40px;
				line-height: 40px;
				color: #666;
				background: #fff;
				width: 100%;
				border: 1px solid #dedede;
				border-radius: 3px;
			}
			.error-box{
				height: 20px;
				line-height: 20px;
				.error-text{
					color: #ff0000;
					display: none;
				}
			}
			
			.error-box-code{
				height: 17px;
			}
		
			
			.code-img-box{
				.code-img{
					display: inline-block;
					width: 90px;
					height: 30px;
					float: left;
				}
				.reset-code{
					padding-left: 10px;
					float: left;
					text-decoration: underline;
					color: #63cdff;
					padding-top: 6px;
				}
			}
			.btn-sub{
				width: 50%;
				height: 40px;
				text-align: center;
				color: #fff;
				line-height: 40px;
				border-radius: 20px;
				background: #59c37c;
				margin: 10px auto;
			}
		}
	}
    
    .redpopup{
		position: fixed;
	    margin: auto;
	    right: 0;
	    left: 0;
	    bottom: 0;
	    top: 0;
	    background-color: rgba(60,60,60,0.55);
	    color: #fff;
	    z-index: 99;
	    .ico{
	    	width: 80%;
	    	margin: 40% auto 0 auto;
	    }
	    
	}
}
