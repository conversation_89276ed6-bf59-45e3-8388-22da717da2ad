'use strict';
define(['module', 'common/kernel/kernel', 'common/zepto/zepto'], function(module, kernel, zepto) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.kuihuareduction");
	$('body').off('touchmove');

	var scrollStar=true;
	var limitPage=10;
	var curPage=1;
	var totalPage;
		/*$dom.scroll(function(){
			if(browser.name==="IOS"){
				try{
					window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
				}catch(err){

				}
			}
			var bheight = $(document).height();//获取窗口高度
			var sheight = kernel.getScrollHeight(dom);//获取滚动条高度，[0]是为了把jq对象转化为js对象
			var stop = $dom.scrollTop();//滚动条距离顶部的距离
			if(stop>=sheight-bheight && stop>1 && scrollStar){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时
				scrollStar=false;
					if(curPage>=totalPage){
						if(totalPage!=0){
							$(".loading-tips").css("display","none");
							$(".nomore-box").empty().append("<p style='text-align:center;padding:10px 0;color:#999'>无更多数据</p>")
						}
					}else{
						$(".loading-tips").show();
						getDataList(curPage);
						curPage++;
						
					}	
				
			}
		});*/

	function getDataList(cur){
		/*var hdid = kernel.location.args.hdid;
		var hdtitle = kernel.location.args.hdtitle;*/
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=ZS201805021142503809&sort=1",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,limit:limitPage,offset:cur},
			dataType: "json",
			success: function(res) {
				var kuihuareductionTemp = " ";
				var kuihuareductionTarget = $(".kuihuareduction-cont");
				totalPage = res.data.pageCount;
				$(".loading-tips").hide();
				for(var i=0;i<res.data.rows.length;i++){
					kuihuareductionTemp+='<div class="kuihuareduction-item">\
								<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo">';
								/*if(res.data.rows[i].blackProductText!='' && res.data.rows[i].blackProductText!= null){
									kuihuareductionTemp+='<span class="nofanmark">'+res.data.rows[i].blackProductText+'</span>';
								};*/
								kuihuareductionTemp+='<img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'"></a>\
								<div class="pro-info">\
								<div class="pro-info-title"><a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="pro-title text-overflow">';
								if(res.data.rows[i].agent == 1){
									kuihuareductionTemp+='<div class="dujia">独家</div>'
								}
								kuihuareductionTemp+= res.data.rows[i].commonName+'</a></div><div class="pro-info-nomal text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
								kuihuareductionTemp+='<div class="biaoqian">';
								for(var j in res.data.rows[i].tagList.slice(0,3)){
									switch(res.data.rows[i].tagList[j].uiType){
										case 1:kuihuareductionTemp+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 2:kuihuareductionTemp+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 3:kuihuareductionTemp+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
										case 4:kuihuareductionTemp+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
										default:;break;
									}
								}
								kuihuareductionTemp+='</div>'	
									if(res.data.rows[i].isControl==1){
										if(res.data.rows[i].isPurchase==true){
											kuihuareductionTemp += '<div class="pro-info-profits">';
											if(res.data.rows[i].uniformPrice){
												kuihuareductionTemp += '<span class="span1">控销价</span class="span2"><span class="span2">￥'+ res.data.rows[i].uniformPrice.toFixed(2)  +'</span>';
											}
											if(res.data.rows[i].suggestPrice){
												kuihuareductionTemp += '<span class="span1">零售价 </span><span class="span2">￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</span>';
											}
											if(res.data.rows[i].grossMargin){
												kuihuareductionTemp += '<span class="span3">毛利</span><span class="span4">'+ parseInt(res.data.rows[i].grossMargin) + '%</span>';
											}
											kuihuareductionTemp += '</div>';
											// if (res.data.rows[i].isSplit==0) {
												// kuihuareductionTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
											/*}else{
												kuihuareductionTemp+='<div class="zhonbao-des"></div>';
											}*/
											kuihuareductionTemp+='<div class="pro-info-bot">';
												if(res.data.rows[i].isControlPriceToMe==1){
													kuihuareductionTemp+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
												}else{
													if(res.data.rows[i].priceType==1){
														kuihuareductionTemp+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
													}else{
														kuihuareductionTemp+='<div class="red bot-price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
													}
												}
													kuihuareductionTemp+='<div class="handle">\
														<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
														<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'"  data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
														<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
													</div>';
												
											kuihuareductionTemp+='</div>';
										}else{
											kuihuareductionTemp+='<div class="pro-info-profits"></div><div class="pro-info-bot">\
												<div class="red bot-price"></div>\
												<div class="lackbuy">暂无购买权限</div>\
											</div>';
										}
									}else{
											kuihuareductionTemp += '<div class="pro-info-profits">';
											if(res.data.rows[i].uniformPrice){
												kuihuareductionTemp += '<span class="span1">控销价</span class="span2"><span class="span2">￥'+ res.data.rows[i].uniformPrice.toFixed(2)  +'</span>';
											}
											if(res.data.rows[i].suggestPrice){
												kuihuareductionTemp += '<span class="span1">零售价 </span><span class="span2">￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</span>';
											}
											if(res.data.rows[i].grossMargin){
												kuihuareductionTemp += '<span class="span3">毛利</span><span class="span4">'+ parseInt(res.data.rows[i].grossMargin) + '%</span>';
											}
											kuihuareductionTemp += '</div>';
											// if (res.data.rows[i].isSplit==0) {
												// kuihuareductionTemp+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
											/*}else{
												kuihuareductionTemp+='<div class="zhonbao-des"></div>';
											}*/
										kuihuareductionTemp+='<div class="pro-info-bot">';
												if(res.data.rows[i].isControlPriceToMe==1){

													kuihuareductionTemp+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
												}else{
													if(res.data.rows[i].priceType==1){
														kuihuareductionTemp+='<div class="red bot-price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
													}else{
														kuihuareductionTemp+='<div class="red bot-price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
													}
												}
												kuihuareductionTemp+='<div class="handle">\
														<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
														<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'"  data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
														<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
													</div>';
											
										kuihuareductionTemp+='</div>';
									}
									kuihuareductionTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="a-link-mask"></a>\
								</div>';
								if(res.data.rows[i].status=="2"){
									kuihuareductionTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
								};
								if(res.data.rows[i].markerUrl){
									kuihuareductionTemp+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
								}
							kuihuareductionTemp+='</div>';
				}
				kuihuareductionTarget.append(kuihuareductionTemp);
				$(".nomore-box").empty().append("<p style='text-align:center;padding:5px 0;color:#999'>无更多数据</p>")
				scrollStar=true;
				listAddMin();
			}
		});
	}
	
	
	return {
		onload: function(force) {
			$(".kuihuareduction-cont").empty();
			$(".nomore-box").empty();
			getDataList(0);
			setAppTitle('葵花满减专场');
		
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});