#page > .content > .conrule {
  overflow-y: auto;
  border-top: none;
  background: #fff;
}
#page > .content > .conrule .main {
  padding: 0 5% 20px 5%;
}
#page > .content > .conrule .title {
  font-weight: 500px;
  font-size: 16px;
  margin-top: 20px;
  height: 35px;
  line-height: 35px;
  background: #f2f2f2;
  padding-left: 15px;
  color: #333;
}
#page > .content > .conrule table {
  border: 1px solid #333;
  text-align: center;
  font-size: 12px;
  margin-top: 10px;
}
#page > .content > .conrule table tr td {
  border-top: 1px solid #333;
  line-height: 18px;
}
#page > .content > .conrule table .tddetail {
  border-left: 1px solid #333;
}
#page > .content > .conrule table tr th {
  line-height: 24px;
}
#page > .content > .conrule .text {
  width: 92%;
  padding: 10px 0;
  margin: 0 auto;
  color: #666;
}
