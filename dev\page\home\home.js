'use strict';
define(['module', 'common/kernel/kernel', 'common/zepto/zepto', 'common/echo/echo'], function(module, kernel, zepto, echo) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);

	kernel.scrollReload(dom);
	$('body').off('touchmove');
	// banner.onchange = function() {
	// 	dom.querySelector('.nav').firstChild.data = navFormat(this.current, this.children.length);
	// };
	
	//var bannertmp;
	//var timer;
	// {
		// 	href:"",
		// 	img:"http://test-upload.ybm100.com/ybm/app/layout/12.15new/SY.png"
		// }
	// var bannerList=[
	// 	{
	// 		href:"ybmpage://DayPreferential/1",
	// 		img:"http://test-upload.ybm100.com/ybm/app/layout/12.15new/SY.png"
	// 	}
		// ,
		// {
		// 	href:"ybmpage://commonh5activity?url="+baseUrl+"/temp/onlinepay.html?ybm_title=玩转在线支付",
		// 	img:"http://**********/ybm/app/layout/12.24new/banner_2.png"
		// }
	//];
	//banner.children=[];
	// for(var i=0; i<bannerList.length; i++){
	// 	bannertmp = document.createElement('a');
	// 	bannertmp.className = 'item';
	// 	if(bannerList[i].href==""||bannerList[i].href=="#"){
	// 		bannertmp.href = "javascript:;";
	// 	}else{
	// 		bannertmp.href = bannerList[i].href;
	// 	}
		
	// 	bannertmp.style.backgroundImage = 'url('+bannerList[i].img+')';
		
	// 	banner.add(bannertmp);

	// }
	// banner.onchange();
	
//头条滚动
	// var topVal = 0;
	// var inx = 0;
	// var lastInx = $(".notice-item:last").index();
	// setInterval(function(){
	// 	topVal = topVal-45;
		
	// 	if(inx>=lastInx){
	// 		$(".notice-scoll").animate({top:"0"}, 200);
	// 		inx=0;
	// 		topVal=0;
	// 	}else{
	// 		$(".notice-scoll").animate({top:topVal+"px"}, 200);
	// 		inx=inx+1;
	// 	}
		
	// },3000);

	dom.onscroll = function(){  
	    loadImg(aImages);
	    try{
    	    //window.hybrid.onScroll(dom.scrollTop); //android
   	    	window.webkit.messageHandlers.Scroll.postMessage({scrolltop:dom.scrollTop});//ios
    	}catch(erro){

    	}
	    
	};
	
   

	function homeDataReload(){
		
		//判断是否新手
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/fetchIsNewExclusiveState",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId},
			dataType: "json",
			success: function(data) {
				if(data.status=="success"){
					if(data.isNewExclusive==true){
						$(".home-ads1").css("display","block");
					}else{
						$(".home-ads1").css("display","none");
					}
				}
			}
		})
		
		
		$.ajax({
			type: "POST",
			url: apiUrl+"/app/layout/fetchDailyDiscountData?dayFlag=1",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId},
			dataType: "json",
			async:true,
			success: function(res) {
				if(res.status=="success"){
					var hotdayTemp=" ";
					var hotdayTarget=$(".hot-day-cont");
					if(res.data.rows.length>0){
						for(var i=0; i<res.data.rows.length;i++){
							hotdayTemp+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="hot-day-item">\
								<img class="photo" src="/static/public/200x200.png" data-original="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">\
								<div class="name text-overflow">'+res.data.rows[i].commonName+'</div>\
								<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>\
								<div class="norms text-overflow">'+res.data.rows[i].spec+'</div>';
								if(res.data.rows[i].status=="2"){
										hotdayTemp+='<div class="gone-icon"></div>';
									}
							hotdayTemp+='</a>';
						}	
						hotdayTarget.empty().append(hotdayTemp);
						loadImg(aImages); 
					}else{
						$(".hot-day").css("display","none");
					}
					
				}
			}
		});		

		
	}
	

	

	return {
		onload: function() {
			//clearInterval(timer);
			//banner.startPlay(10000);
			homeDataReload();
			// try{
		 //        WebViewJavascriptBridge.callHandler('JSGetVersion', null, function(response) {
		 //           // alert('扫描结果:' + response);
		 //        	alert("最新版");
		        	

		 //        })
		 //    }catch(erro){
		 //   	}
		},
		onunload: function() {
			//banner.stopPlay();
		}
	};

	// function navFormat(t, num) {
	// 	var a = '●',
	// 		b = '○',
	// 		txt = '';
	// 	for (var i = 0; i < num; i++) {
	// 		txt += (i === t) ? a : b;
	// 	}
	// 	return txt;
	// }

	// function showForeign(o, url) {
	// 	o.addEventListener('click', function(evt) {
	// 		kernel.showForeign(url);
	// 	}, false);
	// }
/*
	function get_unix_time(dateStr){
		var newStr = dateStr+":00:00";
		newStr = newStr.replace(/-/g,'/');
		newStr = Date.parse(new Date(newStr));
	    return newStr
	}
	*/
});

// function setupWebViewJavascriptBridge(callback) {
//     if (window.WebViewJavascriptBridge) { return callback(WebViewJavascriptBridge); }
//     if (window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
//     window.WVJBCallbacks = [callback];
//     var WVJBIframe = document.createElement('iframe');
//     WVJBIframe.style.display = 'none';
//     WVJBIframe.src = 'wvjbscheme://__BRIDGE_LOADED__';
//     document.documentElement.appendChild(WVJBIframe);
//     setTimeout(function() { document.documentElement.removeChild(WVJBIframe) }, 0)
// }

// setupWebViewJavascriptBridge(function(bridge) {
//      bridge.registerHandler('testJSFunction', function(data, responseCallback) {
//         //alert('JS方法被调用:'+data);
//         responseCallback('js执行过了');
//      })
// })