'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	var $dom = $("#page>.content>.newprocq");
	var scrollTop1;
	var scrollTop2;

	var titHtop = $(".tab-title-senc").offset().top;
    var titHeight = $(".tab-title").height();
    $(".tab-title-box").css("height",titHeight);

	$(".tab-title-item1").click(function (){  
        $dom.animate({  
            scrollTop: scrollTop1-titHeight
        }, 0);  
    });
    $(".tab-title-item2").click(function (){  
        $dom.animate({  
            scrollTop: scrollTop2-titHeight-9
        }, 0);  
    });

    
    
    $dom.scroll(function(){
    	if(browser.name==="IOS"){
			try{
				window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"});//ios
			}catch(err){

			}
		}
		var sHtop = $dom.scrollTop();//滚动条距离顶部的距离
		if(sHtop>titHtop+titHeight){//当滚动条到顶部的距离等于滚动条高度减去窗口高度时
			$(".tab-title").addClass("fixed");
			
		}else if(sHtop<titHtop+titHeight){
			$(".tab-title").removeClass("fixed");
		};
		sHtop = sHtop+titHeight+10;
		if(sHtop>=scrollTop1 && sHtop<scrollTop2){
			$(".tab-title-item1 img").attr("src","/static/public/events/0901/xin2c.jpg");
			$(".tab-title-item2 img").attr("src","/static/public/events/0901/xin3.jpg");
		}else if(sHtop>=scrollTop2){
			$(".tab-title-item1 img").attr("src","/static/public/events/0901/xin2.jpg");
			$(".tab-title-item2 img").attr("src","/static/public/events/0901/xin3c.jpg");
		}
	});

	$.ajax({
		type: "POST",
		url: apiUrl+"/app/layout/initExhibitionModulePage?exhibitionId=ZS201806131118354696&sort=1&limit=40",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId},
		dataType: "json",
		success: function(res) {
			var newprocqTemp1 = " ";
			var newprocqTarget1 = $(".newprocq-cont1");
		for(var i=0;i<res.data.rows.length;i++){
				newprocqTemp1+='<div class="newprocq-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					newprocqTemp1 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							newprocqTemp1 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							newprocqTemp1 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							newprocqTemp1 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					newprocqTemp1+='</div></a>'
				}
				newprocqTemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							newprocqTemp1+='<div class="dujia">独家</div>'
						}
					newprocqTemp1+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				newprocqTemp1+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				newprocqTemp1+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:newprocqTemp1+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:newprocqTemp1+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:newprocqTemp1+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:newprocqTemp1+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				newprocqTemp1+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									newprocqTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										newprocqTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										newprocqTemp1+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// newprocqTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										newprocqTemp1+='<div class="zhonbao-des"></div>';
									}*/
										newprocqTemp1+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								newprocqTemp1+='<div class="price">暂无购买权限</div>';
								newprocqTemp1 += '<div class="control-box"></div>';
								// newprocqTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								newprocqTemp1+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							newprocqTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								newprocqTemp1+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								newprocqTemp1+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// newprocqTemp1+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							newprocqTemp1+='<div class="zhonbao-des"></div>';
						}*/
							newprocqTemp1+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						newprocqTemp1+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						newprocqTemp1+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				newprocqTemp1+='</div>';
			}
			newprocqTarget1.empty().append(newprocqTemp1);
			scrollTop1 = $("#sumtitle1").offset().top;
			rowAddMin();
			
		}
	});


	$.ajax({
		type: "POST",
		url: apiUrl+"/app/sku/findSkuInfo",
		headers : {'version':version,'terminalType':terminalType},
		data: {"merchantId":merchantId,"direction":"desc","property":"s.create_time","limit":"100","offset":"0"},
		dataType: "json",
		success: function(res) {
			var newprocqTemp2 = " ";
			var newprocqTarget2 = $(".newprocq-cont2");
			for(var i=0;i<res.data.rows.length;i++){
				newprocqTemp2+='<div class="newprocq-item">\<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="photo"><img src="'+hostUrl+'/ybm/product/min/'+res.data.rows[i].imageUrl+'">';
				if(res.data.rows[i].isControl!=1 || (res.data.rows[i].isControl==1 && res.data.rows[i].isPurchase==true)){
					newprocqTemp2 += '<div class="control-box">';
						if(res.data.rows[i].uniformPrice){
							newprocqTemp2 += '<div class="control-price"><span>控销价 </span><i>￥'+ res.data.rows[i].uniformPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].suggestPrice){
							newprocqTemp2 += '<div class="control-price"><span>零售价 </span><i>￥'+ res.data.rows[i].suggestPrice.toFixed(2) +'</i></div>'
						};
						if(res.data.rows[i].grossMargin){
							newprocqTemp2 += '<div class="control-gross"><span>毛利</span><i>' +parseInt(res.data.rows[i].grossMargin) +'%</i></div>'
						};
					newprocqTemp2+='</div></a>'
				}
				newprocqTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="commonName" >';
						if(res.data.rows[i].agent == 1){
							newprocqTemp2+='<div class="dujia">独家</div>'
						}
					newprocqTemp2+='<div class="name text-overflow">'+res.data.rows[i].commonName+'</div></a>';	
				newprocqTemp2+='<div class="norms text-overflow">'+res.data.rows[i].spec+'<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div></div>';
				newprocqTemp2+='<div class="biaoqian">';
				for(var j in res.data.rows[i].tagList.slice(0,3)){
					switch(res.data.rows[i].tagList[j].uiType){
						case 1:newprocqTemp2+='<div class="linqi">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 2:newprocqTemp2+='<div class="quan">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 3:newprocqTemp2+='<div class="normal">'+res.data.rows[i].tagList[j].name+'</div>';break;
						case 4:newprocqTemp2+='<div class="fubiao">'+res.data.rows[i].tagList[j].name+'</div>';break;
						default:;break;
					}
				}
				newprocqTemp2+='</div>'
					if(res.data.rows[i].isControl==1){
							if(res.data.rows[i].isPurchase==true){
								if(res.data.rows[i].isControlPriceToMe==1){
									newprocqTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
								}else{
									if(res.data.rows[i].priceType==1){
										newprocqTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
									}else{
										newprocqTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
									}
								}
								
									// if (res.data.rows[i].isSplit==0) {
										// newprocqTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
									/*}else{
										newprocqTemp2+='<div class="zhonbao-des"></div>';
									}*/
										newprocqTemp2+='<div class="handle">\
												<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
												<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
												<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
											</div>';
									
							}else{
								newprocqTemp2+='<div class="price">暂无购买权限</div>';
								newprocqTemp2 += '<div class="control-box"></div>';
								// newprocqTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div><div class="handle-lon"></div>';
								newprocqTemp2+='<div class="handle-lon"></div>';
							}
					}else{
						if(res.data.rows[i].isControlPriceToMe==1){
							newprocqTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
						}else{
							if(res.data.rows[i].priceType==1){
								newprocqTemp2+='<div class="price">￥'+res.data.rows[i].fob.toFixed(2)+'</div>';
							}else{
								newprocqTemp2+='<div class="price">￥'+res.data.rows[i].skuPriceRangeList[0].price.toFixed(2)+' ~ '+res.data.rows[i].skuPriceRangeList[res.data.rows[i].skuPriceRangeList.length-1].price.toFixed(2)+'</div>';
							}
						}
			
						// if (res.data.rows[i].isSplit==0) {
							// newprocqTemp2+='<div class="zhonbao-des">中包装:'+res.data.rows[i].mediumPackageNum+'</div>';
						/*}else{
							newprocqTemp2+='<div class="zhonbao-des"></div>';
						}*/
							newprocqTemp2+='<div class="handle">\
									<span class="add" data-addNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">＋</span>\
									<input type="tel" name="value" class="inp-total inp-total-'+res.data.rows[i].id+'" value="'+res.data.rows[i].cartProductNum+'" data-val="'+res.data.rows[i].cartProductNum+'" data-id="'+res.data.rows[i].id+'" data-inpNum="'+res.data.rows[i].mediumPackageNum+'" data-isSplit="'+res.data.rows[i].isSplit+'">\
									<span class="min" data-minNum="'+res.data.rows[i].mediumPackageNum+'" data-split="'+res.data.rows[i].isSplit+'">－</span>\
								</div>';
						
					}
					
				
					if(res.data.rows[i].status=="2"){
						newprocqTemp2+='<a href="ybmpage://productdetail?product_id='+res.data.rows[i].id+'" class="gone-icon"></a>';
					};
					if(res.data.rows[i].markerUrl){
						newprocqTemp2+='<div class="markerUrl"><img src="'+hostUrl+res.data.rows[i].markerUrl+'"></div>';
					}
					
				newprocqTemp2+='</div>';
			}
			newprocqTarget2.append(newprocqTemp2);
			setTimeout(function(){
				scrollTop2=$("#sumtitle2").offset().top+10;
			},1000);
			
			rowAddMin();
			
		}
	});

	
	return {
		onload: function(force) {
		
		},
		onloadend: function() {
			//this page is open
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});