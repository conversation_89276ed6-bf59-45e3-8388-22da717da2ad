'use strict';
define(['module', 'common/kernel/kernel', 'common/jquery/jquery'], function(module, kernel, jquery) {
	var thispage = module.id.replace(/^[^/]+\/|\/[^/]+/g, ''),
		dom = document.querySelector('#page>.content>.' + thispage);
		kernel.fixIosScrolling(dom);
	$(".redpopup").click(function(){
		$(this).hide();
		$(".inp-sel").val("1");
		$(".textarea").val("");
		$(".photo-inp").val("");
		$("#uploaderInput").val("");
		$(".photo-item-img > *").remove();
		$(".uploader-photo").hide();
		$(".uploader-inp-box").show();
		$("#sp").text("500");
	});
	
    $(".type-item").click(function(){
        var ind=$(this).index();
        $(".type-item").removeClass("type-item-cur").eq(ind).addClass("type-item-cur");
    });

	function checkText(){
		var tarea=$("#textArea");
        var maxlength=500;
        var length=tarea.val().length;
        var count=maxlength-length;
		var sp=$("#sp");
        sp.text(count);
	};

	$("#textArea").keyup(checkText);
  	$("#textArea").bind("paste",function(){
  		setTimeout(function(){
  			checkText();
  		},100)
  	}); 
	
	$(".textarea").blur(function(){
		var textarea = $(".textarea").val();
		if(textarea==""){
            $('.error-text-textarea').text("反馈内容不能为空！").stop().fadeIn(300).delay(1000).fadeOut(300);
            return false;
        };
	});
	
	$(".photo-inp").blur(function(){
        var mobile = $(".photo-inp").val();
           
        if(mobile==""){
            $('.error-text-phone').text("手机不能为空！").stop().fadeIn(300).delay(1000).fadeOut(300);
            return false;
        };
        if(!(/^(0|86|17951)?(13[0-9]|15[012356789]|17[3678]|18[0-9]|14[57])[0-9]{8}$/.test(mobile))){
            $('.error-text-phone').text("手机号格式不正确").fadeIn(300).delay(1000).fadeOut(300);
            return false;
        };
      
    });

	var imgurl="";
	function preview(file) {
	    var prevDiv = $('.photo-item-img');
	    if (file.files && file.files[0]) {
	      var reader = new FileReader();
	      reader.onload = function(evt) {
	      	prevDiv.html('<img src="' + evt.target.result + '" />');
	      	imgurl = evt.target.result;
	      }
	      reader.readAsDataURL(file.files[0]);
	     	$(".uploader-inp-box").hide();
			$(".delbtn").show();
	    } 
	    // else {
	    //   prevDiv.html = '<div class="img" style="filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale,src=\'' + file.value + '\'"></div>';
	    // 	console.log("12.");
	    // }
	}
	$("#uploaderInput").change(function(){
		preview(this);
		doUpload();
	});
	$(".delbtn").click(function(){
		 $(".uploader-photo").hide();
		$(".photo-item-img > *").remove();
		$(".uploader-inp-box").show();
		$(this).hide();
		$("#uploaderInput").val("");
	});
	function doUpload() {
        var formData = new FormData($("#imageform")[0]);
        $.ajax({
            url: apiUrl+'/app/feedback/uploadFeedbackImg',
            type: 'POST',
            headers : {'version':version,'terminalType':terminalType},
            dataType: "json",
            data: formData,
            contentType: false,
            processData: false,
            success: function(returndata) {
                $(".uploader-photo").css("display","inline-block");
                imgurl=returndata.fileName[0];

            },
            error: function(returndata) {
                console.log(returndata)
            }
        });
    }

	$(".btn-sub").click(function(){
		var fdtype=$(".type-item-cur").attr("data-id");
		var areaval = $(".textarea").val();
		if(areaval==""){
            $('.error-text-textarea').text("反馈内容不能为空！").stop().fadeIn(300).delay(1000).fadeOut(300);
            return false;
        };
		var mb=$(".photo-inp").val();
		if(mb==""){
            $('.error-text-phone').text("手机不能为空！").stop().fadeIn(300).delay(1000).fadeOut(300);
            return false;
        };
        if(!(/^(0|86|17951)?(13[0-9]|15[012356789]|17[3678]|18[0-9]|14[57])[0-9]{8}$/.test(mb))){
            $('.error-text-phone').text("手机号格式不正确").fadeIn(300).delay(1000).fadeOut(300);
            return false;
        };
    

        $.ajax({
			type: "POST",
			url: apiUrl+"/app/feedback/saveFeedBack",
			headers : {'version':version,'terminalType':terminalType},
			data: {"merchantId":merchantId,"type":fdtype,"text":areaval,"imagelistUrl":imgurl,"mobile":mb},
			dataType: "json",
			success: function(data) {
				if(data.status=="success"){
					$(".redpopup").show();
					
				}else{
					kernel.hint("网络异常，请重试。"); 
				}
			}
		});
	});
	return {
		onload: function(force) {
			
		},
		onloadend: function() {
			//this page is open
			
		},
        onunload: function() {
            //leveing this page
        },
        onunloadend: function() {
            //left this page
        }
        // 除以上事件外还可包含以下属性
		// * onleftmenuclick 左上角dom点击事件
		// * leftMenuDomContent 左上角dom对象, 字符串表示只显示相应文本
		// * onrightmenuclick 右上角dom点击事件
		// * rightMenuDomContent 右上角dom对象, 字符串表示只显示相应文本
	};
});